#!/usr/bin/env -S deno run --allow-net --allow-env

/**
 * 简化的UUID迁移脚本
 * 避免CockroachDB回填问题，只创建新的UUID表
 */

import { getDbClient } from '../src/utils/database.ts'

// 启用UUID扩展
async function enableUuidExtension() {
  const client = await getDbClient()
  try {
    await client.queryArray('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')
    console.log('✅ UUID扩展已启用')
  } catch (error) {
    console.error('❌ 启用UUID扩展失败:', error)
    throw error
  } finally {
    await client.end()
  }
}

// 创建使用UUID主键的新表
async function createUuidTables() {
  const client = await getDbClient()
  try {
    // 审计日志表
    await client.queryArray(`
      CREATE TABLE IF NOT EXISTS sexup_audit_logs (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id INTEGER REFERENCES sexup_users(id),
        action VARCHAR(50) NOT NULL,
        resource VARCHAR(50) NOT NULL,
        resource_id VARCHAR(50),
        details JSONB,
        ip_address INET,
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `)
    console.log('✅ 审计日志表创建完成')

    // 通知表
    await client.queryArray(`
      CREATE TABLE IF NOT EXISTS sexup_notifications (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id INTEGER REFERENCES sexup_users(id),
        title VARCHAR(200) NOT NULL,
        message TEXT NOT NULL,
        type VARCHAR(20) DEFAULT 'info',
        is_read BOOLEAN DEFAULT FALSE,
        expires_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `)
    console.log('✅ 通知表创建完成')

    // 文件管理表
    await client.queryArray(`
      CREATE TABLE IF NOT EXISTS sexup_files (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id INTEGER REFERENCES sexup_users(id),
        filename VARCHAR(255) NOT NULL,
        original_name VARCHAR(255) NOT NULL,
        mime_type VARCHAR(100) NOT NULL,
        file_size BIGINT NOT NULL,
        file_path TEXT NOT NULL,
        is_public BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `)
    console.log('✅ 文件管理表创建完成')

    // 系统设置表（使用UUID主键）
    await client.queryArray(`
      CREATE TABLE IF NOT EXISTS sexup_system_settings (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        key VARCHAR(100) UNIQUE NOT NULL,
        value TEXT,
        description TEXT,
        is_public BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `)
    console.log('✅ 系统设置表创建完成')

    // 创建索引
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON sexup_audit_logs(user_id);
      CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON sexup_audit_logs(action);
      CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON sexup_audit_logs(created_at);
      
      CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON sexup_notifications(user_id);
      CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON sexup_notifications(is_read);
      CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON sexup_notifications(created_at);
      
      CREATE INDEX IF NOT EXISTS idx_files_user_id ON sexup_files(user_id);
      CREATE INDEX IF NOT EXISTS idx_files_is_public ON sexup_files(is_public);
      CREATE INDEX IF NOT EXISTS idx_files_created_at ON sexup_files(created_at);
      
      CREATE INDEX IF NOT EXISTS idx_system_settings_key ON sexup_system_settings(key);
    `)
    console.log('✅ 索引创建完成')

  } finally {
    await client.end()
  }
}

// 插入一些示例数据
async function insertSampleData() {
  const client = await getDbClient()
  try {
    // 插入系统设置示例
    await client.queryArray(`
      INSERT INTO sexup_system_settings (key, value, description, is_public) 
      VALUES 
        ('site_name', 'SexUp评估系统', '网站名称', true),
        ('max_file_size', '10485760', '最大文件上传大小（字节）', false),
        ('enable_notifications', 'true', '是否启用通知功能', false)
      ON CONFLICT (key) DO NOTHING
    `)
    console.log('✅ 示例系统设置插入完成')

  } catch (error) {
    console.warn('⚠️ 插入示例数据失败:', error)
  } finally {
    await client.end()
  }
}

// 检查迁移结果
async function verifyMigration() {
  const client = await getDbClient()
  try {
    // 检查UUID扩展
    const extensions = await client.queryObject(`
      SELECT extname FROM pg_extension WHERE extname = 'uuid-ossp'
    `)
    
    if (extensions.rows.length > 0) {
      console.log('✅ UUID扩展验证通过')
    } else {
      console.log('❌ UUID扩展未找到')
    }

    // 检查新表
    const tables = [
      'sexup_audit_logs',
      'sexup_notifications', 
      'sexup_files',
      'sexup_system_settings'
    ]

    for (const table of tables) {
      const result = await client.queryObject(`
        SELECT table_name FROM information_schema.tables 
        WHERE table_name = $1
      `, [table])
      
      if (result.rows.length > 0) {
        console.log(`✅ 表 ${table} 验证通过`)
      } else {
        console.log(`❌ 表 ${table} 未找到`)
      }
    }

    // 检查UUID主键
    const uuidTables = await client.queryObject(`
      SELECT t.table_name, c.column_name, c.data_type
      FROM information_schema.tables t
      JOIN information_schema.columns c ON t.table_name = c.table_name
      WHERE t.table_name IN ('sexup_audit_logs', 'sexup_notifications', 'sexup_files', 'sexup_system_settings')
      AND c.column_name = 'id'
      AND c.data_type = 'uuid'
    `)

    console.log(`✅ 找到 ${uuidTables.rows.length} 个使用UUID主键的新表`)

  } finally {
    await client.end()
  }
}

async function main() {
  console.log('=== 简化UUID迁移脚本 ===')
  console.log('这将创建使用UUID主键的新表，避免现有表的回填问题')
  console.log('')

  try {
    console.log('1. 启用UUID扩展...')
    await enableUuidExtension()

    console.log('\n2. 创建UUID表...')
    await createUuidTables()

    console.log('\n3. 插入示例数据...')
    await insertSampleData()

    console.log('\n4. 验证迁移结果...')
    await verifyMigration()

    console.log('\n🎉 简化UUID迁移完成！')
    console.log('')
    console.log('已创建的UUID表：')
    console.log('- sexup_audit_logs (审计日志)')
    console.log('- sexup_notifications (通知)')
    console.log('- sexup_files (文件管理)')
    console.log('- sexup_system_settings (系统设置)')
    console.log('')
    console.log('现有表保持SERIAL主键不变，新功能可以使用UUID主键')

  } catch (error) {
    console.error('❌ 简化UUID迁移失败:', error)
    process.exit(1)
  }
}

if (import.meta.main) {
  main()
}
