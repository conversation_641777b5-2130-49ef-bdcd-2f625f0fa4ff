#!/usr/bin/env -S deno run --allow-net --allow-env

/**
 * 完整的数据库重置脚本（使用UUID主键）
 * 包含：
 * 1. 删除所有现有表
 * 2. 重新创建使用UUID主键的表结构
 * 3. 初始化RBAC权限系统
 * 4. 创建系统角色用户
 */

import { getDbClient } from '../src/utils/database.ts'
import { initDatabase } from '../src/utils/database.ts'

async function dropAllTables() {
  console.log('删除所有现有表...')
  
  const client = await getDbClient()
  try {
    // 删除所有表（按依赖关系顺序）
    const tables = [
      'sexup_user_roles',
      'sexup_role_permissions', 
      'sexup_evaluations',
      'sexup_shares',
      'sexup_api_configs',
      'sexup_evaluation_styles',
      'sexup_evaluation_perspectives',
      'sexup_evaluation_details',
      'sexup_permissions',
      'sexup_roles',
      'sexup_users'
    ]

    for (const table of tables) {
      try {
        await client.queryArray(`DROP TABLE IF EXISTS ${table} CASCADE`)
        console.log(`✅ 删除表: ${table}`)
      } catch (error) {
        console.warn(`⚠️ 删除表 ${table} 失败:`, error)
      }
    }

    console.log('所有表删除完成')
  } finally {
    await client.end()
  }
}

async function main() {
  console.log('=== 数据库完整重置脚本（UUID版本）===')
  console.log('⚠️  警告：这将删除所有现有数据！')
  console.log('')

  try {
    // 第1步：删除所有现有表
    await dropAllTables()
    console.log('')

    // 第2步：重新初始化数据库（使用UUID主键）
    console.log('重新初始化数据库...')
    await initDatabase()
    console.log('')

    console.log('🎉 数据库重置完成！')
    console.log('')
    console.log('已创建的表（使用UUID主键）：')
    console.log('✅ sexup_users - 用户表')
    console.log('✅ sexup_roles - 角色表')
    console.log('✅ sexup_permissions - 权限表')
    console.log('✅ sexup_role_permissions - 角色权限关联表')
    console.log('✅ sexup_user_roles - 用户角色关联表')
    console.log('✅ sexup_evaluations - 评估表')
    console.log('✅ sexup_evaluation_styles - 评估风格表')
    console.log('✅ sexup_evaluation_perspectives - 评估视角表')
    console.log('✅ sexup_evaluation_details - 评估详细度表')
    console.log('✅ sexup_api_configs - API配置表')
    console.log('✅ sexup_shares - 分享表')
    console.log('')
    console.log('已创建的系统用户：')
    console.log('👤 <EMAIL> (密码: super@123456)')
    console.log('👤 <EMAIL> (密码: super@123456)')
    console.log('👤 <EMAIL> (密码: super@123456)')
    console.log('👤 <EMAIL> (密码: super@123456)')
    console.log('👤 <EMAIL> (密码: super@123456)')
    console.log('👤 <EMAIL> (密码: super@123456)')
    console.log('')
    console.log('现在可以启动应用程序，不会再看到SERIAL主键警告！')
    
  } catch (error) {
    console.error('❌ 数据库重置失败:', error)
    Deno.exit(1)
  }
}

if (import.meta.main) {
  main()
}
