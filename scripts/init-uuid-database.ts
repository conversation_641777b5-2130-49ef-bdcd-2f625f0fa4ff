#!/usr/bin/env -S deno run --allow-net --allow-env

/**
 * UUID数据库初始化脚本
 * 使用方法：deno run --allow-net --allow-env scripts/init-uuid-database.ts
 */

import { initDatabaseWithUuid } from '../src/utils/database-uuid.ts'

async function main() {
  console.log('=== UUID数据库初始化脚本 ===')
  console.log('这将创建使用UUID主键的全新数据库结构')
  console.log('注意：这会创建新表，如果表已存在则跳过')
  console.log('')

  try {
    console.log('开始UUID数据库初始化...')
    await initDatabaseWithUuid()
    
    console.log('')
    console.log('🎉 UUID数据库初始化完成！')
    console.log('')
    console.log('已创建的表（使用UUID主键）：')
    console.log('✅ sexup_users - 用户表')
    console.log('✅ sexup_roles - 角色表')
    console.log('✅ sexup_permissions - 权限表')
    console.log('✅ sexup_role_permissions - 角色权限关联表')
    console.log('✅ sexup_user_roles - 用户角色关联表')
    console.log('✅ sexup_evaluations - 评估表')
    console.log('✅ sexup_evaluation_styles - 评估风格表')
    console.log('✅ sexup_evaluation_perspectives - 评估视角表')
    console.log('✅ sexup_evaluation_details - 评估详细度表')
    console.log('✅ sexup_api_configs - API配置表')
    console.log('✅ sexup_shares - 分享表（保持VARCHAR主键）')
    console.log('')
    console.log('已创建超级管理员用户：')
    console.log('用户名: super')
    console.log('邮箱: <EMAIL>')
    console.log('密码: super123456')
    console.log('')
    console.log('现在可以启动应用程序，不会再看到SERIAL主键警告！')
    
  } catch (error) {
    console.error('❌ UUID数据库初始化失败:', error)
    Deno.exit(1)
  }
}

if (import.meta.main) {
  main()
}
