#!/usr/bin/env -S deno run --allow-net --allow-env

/**
 * 测试系统用户创建脚本
 */

import { ensureSystemUsersExist } from '../src/utils/database.ts'

async function main() {
  console.log('=== 测试系统用户创建 ===')
  console.log('')

  try {
    await ensureSystemUsersExist()
    
    console.log('')
    console.log('🎉 系统用户创建/更新完成！')
    console.log('')
    console.log('已创建/更新的系统用户：')
    console.log('✅ super_admin - 用户名: super_admin, 邮箱: <EMAIL>, 密码: super@123456')
    console.log('✅ admin - 用户名: admin, 邮箱: <EMAIL>, 密码: super@123456')
    console.log('✅ moderator - 用户名: moderator, 邮箱: <EMAIL>, 密码: super@123456')
    console.log('✅ vip_user - 用户名: vip_user, 邮箱: <EMAIL>, 密码: super@123456')
    console.log('✅ regular_user - 用户名: regular_user, 邮箱: <EMAIL>, 密码: super@123456')
    console.log('✅ guest - 用户名: guest, 邮箱: <EMAIL>, 密码: super@123456')
    console.log('')
    console.log('所有用户都已分配对应的系统角色权限')
    
  } catch (error) {
    console.error('❌ 系统用户创建失败:', error)
    Deno.exit(1)
  }
}

if (import.meta.main) {
  main()
}
