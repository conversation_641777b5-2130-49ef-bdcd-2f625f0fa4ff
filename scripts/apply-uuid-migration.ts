#!/usr/bin/env -S deno run --allow-net --allow-env

/**
 * UUID迁移应用脚本
 * 使用方法：deno run --allow-net --allow-env scripts/apply-uuid-migration.ts
 */

import { migrateToUuidTables } from '../src/utils/uuid-migration.ts'

async function main() {
  console.log('=== UUID迁移脚本 ===')
  console.log('这将为新表启用UUID主键支持')
  console.log('现有表将保持SERIAL主键不变')
  console.log('')

  try {
    // 执行UUID迁移
    await migrateToUuidTables()
    
    console.log('')
    console.log('✅ UUID迁移完成！')
    console.log('')
    console.log('已完成的操作：')
    console.log('1. 启用了uuid-ossp扩展')
    console.log('2. 为现有表添加了uuid字段（可选）')
    console.log('3. 创建了使用UUID主键的新表：')
    console.log('   - sexup_audit_logs (审计日志)')
    console.log('   - sexup_notifications (通知)')
    console.log('   - sexup_files (文件管理)')
    console.log('')
    console.log('现有表保持不变，继续使用SERIAL主键')
    console.log('新功能可以使用UUID主键获得更好的性能')
    
  } catch (error) {
    console.error('❌ UUID迁移失败:', error)
    process.exit(1)
  }
}

if (import.meta.main) {
  main()
}
