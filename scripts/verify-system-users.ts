#!/usr/bin/env -S deno run --allow-net --allow-env

/**
 * 验证系统用户创建和密码哈希
 * 确保所有系统用户都能正常登录
 */

import { getDbClient } from '../src/utils/database.ts'
import { verifyPassword } from '../src/utils/auth.ts'

// 模拟客户端密码哈希函数（与前端保持一致）
async function hashPasswordClient(password: string): Promise<string> {
  const encoder = new TextEncoder()
  const data = encoder.encode(password + 'client-salt-2024')
  const hashBuffer = await crypto.subtle.digest('SHA-256', data)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
}

async function verifySystemUsers() {
  console.log('=== 验证系统用户 ===')
  console.log('')

  const client = await getDbClient()
  try {
    // 查询所有系统用户
    const result = await client.queryObject(`
      SELECT u.id, u.username, u.email, u.password_hash, u.is_active,
             r.name as role_name, r.display_name as role_display_name
      FROM sexup_users u
      LEFT JOIN sexup_user_roles ur ON u.id = ur.user_id
      LEFT JOIN sexup_roles r ON ur.role_id = r.id
      WHERE u.email LIKE '%@sexup.com'
      ORDER BY u.username
    `)

    if (result.rows.length === 0) {
      console.log('❌ 未找到系统用户')
      console.log('请先运行数据库初始化脚本创建系统用户')
      return false
    }

    console.log(`找到 ${result.rows.length} 个系统用户:`)
    console.log('')

    let allValid = true
    const testPassword = 'super@123456'
    const clientHashed = await hashPasswordClient(testPassword)

    for (const row of result.rows) {
      const user = row as any
      console.log(`👤 用户: ${user.username}`)
      console.log(`   邮箱: ${user.email}`)
      console.log(`   角色: ${user.role_display_name || '未分配'}`)
      console.log(`   状态: ${user.is_active ? '✅ 活跃' : '❌ 禁用'}`)

      // 验证密码
      const passwordValid = await verifyPassword(clientHashed, user.password_hash)
      console.log(`   密码: ${passwordValid ? '✅ 正确' : '❌ 错误'}`)

      if (!passwordValid) {
        allValid = false
      }

      console.log('')
    }

    return allValid

  } finally {
    await client.end()
  }
}

async function testLoginFlow() {
  console.log('=== 测试登录流程 ===')
  console.log('')

  const testUsers = [
    { username: 'super_admin', email: '<EMAIL>' },
    { username: 'admin', email: '<EMAIL>' },
    { username: 'regular_user', email: '<EMAIL>' }
  ]

  const client = await getDbClient()
  try {
    for (const testUser of testUsers) {
      console.log(`测试用户: ${testUser.username}`)

      // 模拟前端登录请求
      const password = 'super@123456'
      const clientHashed = await hashPasswordClient(password)

      // 查询用户
      const result = await client.queryObject(
        'SELECT id, username, email, password_hash FROM sexup_users WHERE username = $1 OR email = $1',
        [testUser.username]
      )

      if (result.rows.length === 0) {
        console.log(`   ❌ 用户不存在`)
        continue
      }

      const user = result.rows[0] as any
      const isValid = await verifyPassword(clientHashed, user.password_hash)

      console.log(`   用户名登录: ${isValid ? '✅ 成功' : '❌ 失败'}`)

      // 测试邮箱登录
      const emailResult = await client.queryObject(
        'SELECT id, username, email, password_hash FROM sexup_users WHERE username = $1 OR email = $1',
        [testUser.email]
      )

      if (emailResult.rows.length > 0) {
        const emailUser = emailResult.rows[0] as any
        const emailValid = await verifyPassword(clientHashed, emailUser.password_hash)
        console.log(`   邮箱登录: ${emailValid ? '✅ 成功' : '❌ 失败'}`)
      }

      console.log('')
    }

  } finally {
    await client.end()
  }
}

async function showLoginInstructions() {
  console.log('=== 登录说明 ===')
  console.log('')
  console.log('🔐 所有系统用户的登录信息:')
  console.log('')
  console.log('密码: super@123456')
  console.log('')
  console.log('可以使用以下任一方式登录:')
  console.log('1. 用户名: super_admin')
  console.log('2. 邮箱: <EMAIL>')
  console.log('')
  console.log('其他系统用户:')
  console.log('- admin / <EMAIL>')
  console.log('- moderator / <EMAIL>')
  console.log('- vip_user / <EMAIL>')
  console.log('- regular_user / <EMAIL>')
  console.log('- guest / <EMAIL>')
  console.log('')
  console.log('💡 提示:')
  console.log('- 前端会自动进行密码哈希，直接输入原始密码即可')
  console.log('- 系统使用双重哈希机制确保安全性')
  console.log('- 如果登录失败，请检查用户是否已创建和激活')
}

async function main() {
  try {
    const usersValid = await verifySystemUsers()
    await testLoginFlow()
    await showLoginInstructions()

    if (usersValid) {
      console.log('🎉 所有系统用户验证通过！')
      console.log('可以使用密码 "super@123456" 登录任何系统用户')
    } else {
      console.log('❌ 部分系统用户验证失败')
      console.log('建议重新运行数据库初始化脚本')
    }

  } catch (error) {
    console.error('❌ 验证失败:', error)
    Deno.exit(1)
  }
}

if (import.meta.main) {
  main()
}
