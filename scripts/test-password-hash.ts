#!/usr/bin/env -S deno run --allow-net --allow-env

/**
 * 测试密码哈希算法
 * 验证系统用户的密码哈希是否与登录时的哈希一致
 */

import { hashPassword, verifyPassword } from '../src/utils/auth.ts'

// 模拟客户端密码哈希函数（与前端保持一致）
async function hashPasswordClient(password: string): Promise<string> {
  const encoder = new TextEncoder()
  const data = encoder.encode(password + 'client-salt-2024')
  const hashBuffer = await crypto.subtle.digest('SHA-256', data)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
}

async function testPasswordHashing() {
  console.log('=== 密码哈希算法测试 ===')
  console.log('')

  const testPassword = 'super@123456'
  
  console.log('1. 测试密码:', testPassword)
  console.log('')

  // 步骤1：客户端哈希
  const clientHashed = await hashPasswordClient(testPassword)
  console.log('2. 客户端哈希结果:')
  console.log('   算法: SHA-256(password + "client-salt-2024")')
  console.log('   结果:', clientHashed)
  console.log('')

  // 步骤2：服务端哈希
  const serverHashed = await hashPassword(clientHashed)
  console.log('3. 服务端哈希结果:')
  console.log('   算法: SHA-256(clientHash + JWT_SECRET)')
  console.log('   结果:', serverHashed)
  console.log('')

  // 步骤3：验证密码
  const isValid = await verifyPassword(clientHashed, serverHashed)
  console.log('4. 密码验证:')
  console.log('   输入客户端哈希:', clientHashed)
  console.log('   存储的服务端哈希:', serverHashed)
  console.log('   验证结果:', isValid ? '✅ 通过' : '❌ 失败')
  console.log('')

  // 步骤4：模拟完整的登录流程
  console.log('5. 模拟登录流程:')
  console.log('   用户输入密码:', testPassword)
  console.log('   前端哈希后发送:', clientHashed)
  console.log('   后端验证结果:', isValid ? '✅ 登录成功' : '❌ 登录失败')
  console.log('')

  if (isValid) {
    console.log('🎉 密码哈希算法测试通过！')
    console.log('系统用户可以使用密码 "super@123456" 正常登录')
  } else {
    console.log('❌ 密码哈希算法测试失败！')
    console.log('需要检查哈希算法的一致性')
  }
}

async function testAllSystemUsers() {
  console.log('')
  console.log('=== 系统用户密码测试 ===')
  console.log('')

  const systemUsers = [
    'super_admin',
    'admin', 
    'moderator',
    'vip_user',
    'regular_user',
    'guest'
  ]

  for (const username of systemUsers) {
    console.log(`测试用户: ${username}`)
    console.log(`邮箱: ${username}@sexup.com`)
    console.log(`密码: super@123456`)
    
    // 模拟前端登录流程
    const clientHashed = await hashPasswordClient('super@123456')
    const serverHashed = await hashPassword(clientHashed)
    const isValid = await verifyPassword(clientHashed, serverHashed)
    
    console.log(`哈希结果: ${isValid ? '✅ 正确' : '❌ 错误'}`)
    console.log('')
  }
}

async function main() {
  try {
    await testPasswordHashing()
    await testAllSystemUsers()
    
    console.log('📋 使用说明:')
    console.log('1. 所有系统用户的密码都是: super@123456')
    console.log('2. 可以使用用户名或邮箱登录')
    console.log('3. 前端会自动进行客户端哈希')
    console.log('4. 后端会进行二次哈希验证')
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
    Deno.exit(1)
  }
}

if (import.meta.main) {
  main()
}
