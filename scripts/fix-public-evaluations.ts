#!/usr/bin/env -S deno run --allow-net --allow-env

/**
 * 修复公开评估问题
 * 1. 检查数据库连接
 * 2. 检查表结构
 * 3. 创建测试数据
 * 4. 测试API
 */

import { getDbClient } from '../src/utils/database.ts'

async function checkDatabaseConnection() {
  console.log('=== 检查数据库连接 ===')
  try {
    const client = await getDbClient()
    await client.queryArray('SELECT 1')
    await client.end()
    console.log('✅ 数据库连接正常')
    return true
  } catch (error) {
    console.log('❌ 数据库连接失败:', error.message)
    return false
  }
}

async function checkTableStructure() {
  console.log('\n=== 检查表结构 ===')
  const client = await getDbClient()
  try {
    // 检查评估表是否存在
    const tableResult = await client.queryObject(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'sexup_evaluations'
      )
    `)
    
    const tableExists = (tableResult.rows[0] as any).exists
    console.log(`评估表存在: ${tableExists ? '✅' : '❌'}`)
    
    if (!tableExists) {
      console.log('❌ 评估表不存在，请运行数据库初始化')
      return false
    }
    
    // 检查is_public字段
    const columnResult = await client.queryObject(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'sexup_evaluations'
        AND column_name = 'is_public'
      )
    `)
    
    const columnExists = (columnResult.rows[0] as any).exists
    console.log(`is_public字段存在: ${columnExists ? '✅' : '❌'}`)
    
    return tableExists && columnExists
    
  } finally {
    await client.end()
  }
}

async function createTestData() {
  console.log('\n=== 创建测试数据 ===')
  const client = await getDbClient()
  try {
    // 查找系统用户
    const userResult = await client.queryObject(`
      SELECT id, username FROM sexup_users 
      WHERE email LIKE '%@sexup.com' 
      LIMIT 1
    `)
    
    if (userResult.rows.length === 0) {
      console.log('❌ 未找到系统用户')
      return false
    }
    
    const user = userResult.rows[0] as any
    console.log(`使用用户: ${user.username}`)
    
    // 检查是否已有公开评估
    const existingResult = await client.queryObject(`
      SELECT COUNT(*) as count FROM sexup_evaluations WHERE is_public = true
    `)
    const existingCount = Number((existingResult.rows[0] as any).count)
    
    if (existingCount > 0) {
      console.log(`✅ 已有 ${existingCount} 个公开评估`)
      return true
    }
    
    // 创建测试评估
    console.log('创建测试公开评估...')
    const result = await client.queryObject(`
      INSERT INTO sexup_evaluations (
        user_id, title, description, image_data, mime_type,
        style, perspective, detail, style_code, perspective_code, detail_code,
        verdict, rating, explanation, api_config, processing_time_ms, is_public,
        created_at, updated_at
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, NOW(), NOW()
      ) RETURNING id, title
    `, [
      user.id,
      '测试公开评估',
      '这是一个测试的公开评估',
      'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=',
      'image/jpeg',
      '文明版本',
      '摄影师',
      '详细',
      'CIVIL',
      'PHOTOGRAPHER',
      'MIDDEN',
      '优秀',
      85,
      '这是一个测试的公开评估，用于验证API功能是否正常工作。',
      JSON.stringify({}),
      0,
      true
    ])
    
    const created = result.rows[0] as any
    console.log(`✅ 创建测试评估: ${created.title} (ID: ${created.id})`)
    return true
    
  } finally {
    await client.end()
  }
}

async function testAPI() {
  console.log('\n=== 测试公开评估API ===')
  try {
    const response = await fetch('http://localhost:8888/api/v1/evaluations/public', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    console.log(`API状态: ${response.status} ${response.statusText}`)
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ API调用成功')
      console.log(`返回评估数量: ${data.evaluations?.length || 0}`)
      
      if (data.evaluations && data.evaluations.length > 0) {
        console.log('评估列表:')
        data.evaluations.forEach((eval: any, index: number) => {
          console.log(`  ${index + 1}. ${eval.title} - ${eval.verdict} (${eval.rating}/100)`)
        })
      }
      return true
    } else {
      const errorData = await response.json()
      console.log('❌ API调用失败:', errorData)
      return false
    }
    
  } catch (error) {
    console.log('❌ API调用异常:', error.message)
    return false
  }
}

async function main() {
  console.log('🔧 修复公开评估问题\n')
  
  try {
    // 1. 检查数据库连接
    const dbOk = await checkDatabaseConnection()
    if (!dbOk) {
      console.log('\n❌ 数据库连接失败，请检查数据库配置')
      return
    }
    
    // 2. 检查表结构
    const tableOk = await checkTableStructure()
    if (!tableOk) {
      console.log('\n❌ 表结构有问题，请运行数据库初始化')
      return
    }
    
    // 3. 创建测试数据
    const dataOk = await createTestData()
    if (!dataOk) {
      console.log('\n❌ 创建测试数据失败')
      return
    }
    
    // 4. 测试API
    const apiOk = await testAPI()
    if (!apiOk) {
      console.log('\n❌ API测试失败')
      return
    }
    
    console.log('\n🎉 公开评估功能修复完成！')
    console.log('\n📋 下一步:')
    console.log('1. 刷新浏览器页面')
    console.log('2. 点击"公开评估"标签')
    console.log('3. 应该能看到测试评估数据')
    
  } catch (error) {
    console.error('\n❌ 修复过程中出现错误:', error)
  }
}

if (import.meta.main) {
  main()
}
