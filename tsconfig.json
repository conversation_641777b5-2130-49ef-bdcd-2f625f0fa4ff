{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "noEmit": true, "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": false, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "verbatimModuleSyntax": true, "allowJs": true, "checkJs": false, "jsx": "react-jsx", "jsxImportSource": "hono/jsx", "types": ["node"]}, "include": ["**/*.ts", "**/*.tsx", "main.ts", "src/**/*"], "exclude": ["node_modules", "static/**/*.js", "default.ts"]}