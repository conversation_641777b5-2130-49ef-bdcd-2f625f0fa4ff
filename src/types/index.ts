// 类型定义文件

import type { Context } from 'hono'

// Hono Context 类型
export type HonoContext = Context

// API 配置类型
export interface ApiConfig {
  baseUrl?: string
  apiKey?: string
  model?: string
}

// 评估请求类型
export interface EvaluationRequest {
  image: string
  mimeType?: string
  style: 'CIVIL' | 'PRAISE' | 'VULGAR' | 'XBAOLOU'
  perspective: 'MALE' | 'FEMALE' | 'PHOTOGRAPHER' | 'ARTIST' | 'FOODIE' | 'NATURE' | 'TRAVEL' | 'FASHION' | 'XBAOLOU'
  detail: 'SMALL' | 'MIDDEN' | 'LARGE' | 'XBAOLOU_SMALL' | 'XBAOLOU_MIDDEN' | 'XBAOLOU_LARGE'
  apiConfig?: ApiConfig
}

// 评估结果类型
export interface EvaluationResult {
  verdict: string
  rating: number
  explanation: string
  timestamp: string
}

// 用户类型
export interface User {
  id: number
  username: string
  email?: string
  isActive?: boolean
  defaultRoleId?: number
  lastLoginAt?: string
  createdAt?: string
  updatedAt?: string
}

// 角色类型
export interface Role {
  id: number
  name: string
  displayName: string
  description?: string
  isSystem: boolean
  createdAt: string
  updatedAt: string
}

// 权限类型
export interface Permission {
  id: number
  name: string
  displayName: string
  description?: string
  resource: string
  action: string
  createdAt: string
}

// 用户角色关联类型
export interface UserRole {
  id: number
  userId: number
  roleId: number
  assignedBy?: number
  assignedAt: string
  expiresAt?: string
}

// 认证响应类型
export interface AuthResponse {
  message: string
  token: string
  user: User
}

// 分享数据类型
export interface ShareData extends EvaluationResult {
  image?: string
  mimeType?: string
  isPublic?: boolean
}

// 分享响应类型
export interface ShareResponse {
  shareId: string
  shareUrl: string
  fullUrl: string
  expiresAt: string
}

// 数据库用户类型
export interface DbUser {
  id: number | bigint
  username: string
  email: string
  password_hash: string
  created_at?: Date
}

// 数据库分享类型
export interface DbShare {
  id: string
  user_id?: number | bigint
  data: string
  is_public: boolean
  created_at: Date
  expires_at: Date
  view_count: number
}

// API 错误响应类型
export interface ApiError {
  error: string
  details?: string
}
