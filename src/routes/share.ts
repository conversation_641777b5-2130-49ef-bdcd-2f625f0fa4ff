import { Hono } from 'hono'
import type { HonoContext } from '../types/index.ts'
import { getDbClient } from '../utils/database.ts'
import { getErrorPage, SHARE_PAGE_TEMPLATE } from '../utils/helpers.ts'

export function setupShareRoutes(app: Hono) {
  // 短链接分享页面
  app.get('/s/:shareId', async (c: HonoContext) => {
    const shareId = c.req.param('shareId')
    
    if (!shareId || !/^[a-zA-Z0-9]{8}$/.test(shareId)) {
      return c.html(getErrorPage('无效的分享链接', '分享链接格式不正确'))
    }
    
    try {
      const client = await getDbClient()
      
      try {
        // 查询分享数据
        const result = await client.queryObject(
          'SELECT * FROM sexup_shares WHERE id = $1 AND expires_at > NOW()',
          [shareId]
        )
        
        if (result.rows.length === 0) {
          return c.html(getErrorPage('分享不存在', '该分享链接不存在或已过期'))
        }
        
        const row = result.rows[0] as any
        const shareData = row.data
        
        // 增加浏览次数
        shareData.viewCount = (shareData.viewCount || 0) + 1
        
        await client.queryArray(
          'UPDATE sexup_shares SET data = $1, view_count = view_count + 1 WHERE id = $2',
          [JSON.stringify(shareData), shareId]
        )
        
        return c.html(SHARE_PAGE_TEMPLATE(shareData, shareId))
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('获取分享数据失败:', error)
      return c.html(getErrorPage('服务器错误', '无法加载分享内容，请稍后重试'))
    }
  })

  // 兼容旧的分享链接格式
  app.get('/share', (c: HonoContext) => {
    const encodedData = c.req.query('data')

    if (!encodedData) {
      return c.html(getErrorPage('分享链接无效', '该分享链接缺少必要的数据参数'))
    }

    try {
      const jsonString = decodeURIComponent(atob(encodedData))
      const shareData = JSON.parse(jsonString)

      if (!shareData.verdict || !shareData.rating || !shareData.explanation) {
        throw new Error('数据不完整')
      }

      return c.html(SHARE_PAGE_TEMPLATE(shareData))
    } catch (_error) {
      return c.html(getErrorPage('分享链接错误', '无法解析分享数据，链接可能已损坏'))
    }
  })
} 
