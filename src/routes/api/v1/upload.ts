import { Hono } from 'hono'
import type { HonoContext } from '../../../types/index.ts'
import { requirePermissions } from '../../../utils/rbac.ts'

/**
 * 文件上传 API
 * 
 * POST /api/v1/upload - 上传文件到CDN
 */

export function setupUploadRoutes(app: Hono) {
  
  // 上传文件到CDN
  app.post('/api/v1/upload', requirePermissions('evaluations:create'), async (c: HonoContext) => {
    try {
      const currentUser = c.get('user')
      if (!currentUser) {
        return c.json({ error: '未登录' }, 401)
      }

      // 获取上传的文件
      const body = await c.req.formData()
      const file = body.get('file') as File
      
      if (!file) {
        return c.json({ error: '请选择要上传的文件' }, 400)
      }

      // 验证文件类型
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
      if (!allowedTypes.includes(file.type)) {
        return c.json({ error: '不支持的文件类型，请上传图片文件' }, 400)
      }

      // 验证文件大小 (10MB)
      const maxSize = 10 * 1024 * 1024
      if (file.size > maxSize) {
        return c.json({ error: '文件大小不能超过10MB' }, 400)
      }

      console.log('开始上传文件到CDN:', {
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
        userId: currentUser.id
      })

      // 创建FormData用于CDN上传
      const uploadFormData = new FormData()
      uploadFormData.append('file', file)

      // 上传到CDN服务
      const uploadResponse = await fetch(
        'https://file.jhun.edu.kg/upload?authCode=root&serverCompress=true&uploadChannel=telegram&autoRetry=true&uploadNameType=default&returnFormat=full&uploadFolder=sexup/',
        {
          method: 'POST',
          headers: {
            'User-Agent': 'FileUploader/1.0.0',
          },
          body: uploadFormData,
        }
      )

      if (!uploadResponse.ok) {
        const errorText = await uploadResponse.text()
        console.error('CDN上传失败:', uploadResponse.status, errorText)
        return c.json({ error: `CDN上传失败: ${uploadResponse.status}` }, 500)
      }

      const uploadResult = await uploadResponse.json()
      console.log('CDN上传成功:', uploadResult)

      // CDN返回的是数组格式，包含src字段
      let imageUrl = null
      if (Array.isArray(uploadResult) && uploadResult.length > 0 && uploadResult[0].src) {
        imageUrl = uploadResult[0].src
      } else if (uploadResult.url) {
        imageUrl = uploadResult.url
      } else if (uploadResult.src) {
        imageUrl = uploadResult.src
      }

      if (!imageUrl) {
        console.error('CDN响应中没有图片URL:', uploadResult)
        return c.json({ error: 'CDN响应格式错误，没有找到图片URL' }, 500)
      }

      // 返回上传结果
      return c.json({
        success: true,
        url: imageUrl,
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
        message: '文件上传成功'
      }, 200)

    } catch (error) {
      console.error('文件上传失败:', error)
      return c.json({ error: '文件上传失败' }, 500)
    }
  })

  // 批量上传文件
  app.post('/api/v1/upload/batch', requirePermissions('evaluations:create'), async (c: HonoContext) => {
    try {
      const currentUser = c.get('user')
      if (!currentUser) {
        return c.json({ error: '未登录' }, 401)
      }

      // 获取上传的文件
      const body = await c.req.formData()
      const files: File[] = []
      
      // 收集所有文件
      for (const [key, value] of body.entries()) {
        if (key.startsWith('file') && value instanceof File) {
          files.push(value)
        }
      }

      if (files.length === 0) {
        return c.json({ error: '请选择要上传的文件' }, 400)
      }

      if (files.length > 10) {
        return c.json({ error: '一次最多上传10个文件' }, 400)
      }

      console.log(`开始批量上传 ${files.length} 个文件到CDN`)

      // 并行上传所有文件
      const uploadPromises = files.map(async (file, index) => {
        try {
          // 验证文件类型
          const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
          if (!allowedTypes.includes(file.type)) {
            throw new Error(`文件 ${file.name}: 不支持的文件类型`)
          }

          // 验证文件大小 (10MB)
          const maxSize = 10 * 1024 * 1024
          if (file.size > maxSize) {
            throw new Error(`文件 ${file.name}: 文件大小不能超过10MB`)
          }

          // 创建FormData用于CDN上传
          const uploadFormData = new FormData()
          uploadFormData.append('file', file)

          // 上传到CDN服务
          const uploadResponse = await fetch(
            'https://file.jhun.edu.kg/upload?authCode=root&serverCompress=true&uploadChannel=telegram&autoRetry=true&uploadNameType=default&returnFormat=full&uploadFolder=pinterest/',
            {
              method: 'POST',
              headers: {
                'User-Agent': 'FileUploader/1.0.0',
              },
              body: uploadFormData,
            }
          )

          if (!uploadResponse.ok) {
            const errorText = await uploadResponse.text()
            throw new Error(`CDN上传失败: ${uploadResponse.status} ${errorText}`)
          }

          const uploadResult = await uploadResponse.json()

          // CDN返回的是数组格式，包含src字段
          let imageUrl = null
          if (Array.isArray(uploadResult) && uploadResult.length > 0 && uploadResult[0].src) {
            imageUrl = uploadResult[0].src
          } else if (uploadResult.url) {
            imageUrl = uploadResult.url
          } else if (uploadResult.src) {
            imageUrl = uploadResult.src
          }

          if (!imageUrl) {
            throw new Error('CDN响应中没有图片URL')
          }

          return {
            success: true,
            url: imageUrl,
            fileName: file.name,
            fileType: file.type,
            fileSize: file.size,
            index
          }
        } catch (error) {
          console.error(`文件 ${file.name} 上传失败:`, error)
          return {
            success: false,
            fileName: file.name,
            error: error instanceof Error ? error.message : String(error),
            index
          }
        }
      })

      const results = await Promise.all(uploadPromises)
      const successCount = results.filter(r => r.success).length
      const failCount = results.filter(r => !r.success).length

      console.log(`批量上传完成: 成功 ${successCount} 个，失败 ${failCount} 个`)

      return c.json({
        success: true,
        results,
        summary: {
          total: files.length,
          success: successCount,
          failed: failCount
        },
        message: `批量上传完成: 成功 ${successCount} 个，失败 ${failCount} 个`
      }, 200)

    } catch (error) {
      console.error('批量文件上传失败:', error)
      return c.json({ error: '批量文件上传失败' }, 500)
    }
  })
}
