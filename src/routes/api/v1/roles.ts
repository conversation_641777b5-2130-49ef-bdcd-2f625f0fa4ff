import { Hono } from 'hono'
import type { HonoContext } from '../../../types/index.ts'
import { getDbClient } from '../../../utils/database.ts'
import { requirePermissions } from '../../../utils/rbac.ts'

/**
 * RESTful Roles API
 * 
 * GET    /api/v1/roles              - 获取角色列表
 * GET    /api/v1/roles/:id          - 获取指定角色
 * POST   /api/v1/roles              - 创建角色
 * PUT    /api/v1/roles/:id          - 更新角色
 * DELETE /api/v1/roles/:id          - 删除角色
 * GET    /api/v1/roles/:id/permissions - 获取角色权限
 * POST   /api/v1/roles/:id/permissions - 分配角色权限
 * DELETE /api/v1/roles/:id/permissions/:permissionId - 移除角色权限
 */

export function setupRolesRoutes(app: Hono) {
  
  // 获取角色列表
  app.get('/api/v1/roles', requirePermissions('roles:read'), async (c: HonoContext) => {
    try {
      const page = parseInt(c.req.query('page') || '1')
      const limit = parseInt(c.req.query('limit') || '50')
      const includeSystem = c.req.query('includeSystem') === 'true'
      const offset = (page - 1) * limit

      const client = await getDbClient()
      try {
        let query = `
          SELECT r.id, r.name, r.display_name, r.description, r.is_system, r.created_at,
                 COUNT(ur.user_id) as user_count
          FROM sexup_roles r
          LEFT JOIN sexup_user_roles ur ON r.id = ur.role_id
        `
        const params: any[] = []

        if (!includeSystem) {
          query += ` WHERE r.is_system = false`
        }

        query += ` GROUP BY r.id, r.name, r.display_name, r.description, r.is_system, r.created_at`
        query += ` ORDER BY r.is_system DESC, r.created_at ASC`
        query += ` LIMIT $${params.length + 1} OFFSET $${params.length + 2}`
        params.push(limit, offset)

        const result = await client.queryObject(query, params)

        // 获取总数
        let countQuery = 'SELECT COUNT(*) as total FROM sexup_roles r'
        const countParams: any[] = []

        if (!includeSystem) {
          countQuery += ` WHERE r.is_system = false`
        }

        const countResult = await client.queryObject(countQuery, countParams)
        const total = Number((countResult.rows[0] as any).total)

        return c.json({
          roles: result.rows,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit)
          }
        })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('获取角色列表失败:', error)
      return c.json({ error: '获取角色列表失败' }, 500)
    }
  })

  // 获取指定角色
  app.get('/api/v1/roles/:id', requirePermissions('roles:read'), async (c: HonoContext) => {
    try {
      const roleId = parseInt(c.req.param('id'))

      const client = await getDbClient()
      try {
        const result = await client.queryObject(`
          SELECT r.*, COUNT(ur.user_id) as user_count
          FROM sexup_roles r
          LEFT JOIN sexup_user_roles ur ON r.id = ur.role_id
          WHERE r.id = $1
          GROUP BY r.id
        `, [roleId])

        if (result.rows.length === 0) {
          return c.json({ error: '角色不存在' }, 404)
        }

        const role = result.rows[0]

        // 获取角色权限
        const permissionsResult = await client.queryObject(`
          SELECT p.id, p.name, p.display_name, p.description, p.resource, p.action
          FROM sexup_permissions p
          INNER JOIN sexup_role_permissions rp ON p.id = rp.permission_id
          WHERE rp.role_id = $1
          ORDER BY p.resource, p.action
        `, [roleId])

        return c.json({
          role: {
            ...(role as any),
            permissions: permissionsResult.rows
          }
        })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('获取角色信息失败:', error)
      return c.json({ error: '获取角色信息失败' }, 500)
    }
  })

  // 创建角色
  app.post('/api/v1/roles', requirePermissions('roles:create'), async (c: HonoContext) => {
    try {
      const { name, displayName, description } = await c.req.json()

      if (!name || !displayName) {
        return c.json({ error: '角色名称和显示名称不能为空' }, 400)
      }

      // 验证角色名称格式
      if (!/^[a-z_]+$/.test(name)) {
        return c.json({ error: '角色名称只能包含小写字母和下划线' }, 400)
      }

      const client = await getDbClient()
      try {
        const result = await client.queryObject(`
          INSERT INTO sexup_roles (name, display_name, description, is_system)
          VALUES ($1, $2, $3, false)
          RETURNING id, name, display_name, description, is_system, created_at
        `, [name, displayName, description || ''])

        return c.json({
          message: '角色创建成功',
          role: result.rows[0]
        }, 201)
      } finally {
        await client.end()
      }
    } catch (error: any) {
      console.error('创建角色失败:', error)
      if (error.message?.includes('duplicate key')) {
        return c.json({ error: '角色名称已存在' }, 400)
      }
      return c.json({ error: '创建角色失败' }, 500)
    }
  })

  // 更新角色
  app.put('/api/v1/roles/:id', requirePermissions('roles:update'), async (c: HonoContext) => {
    try {
      const roleId = parseInt(c.req.param('id'))
      const { displayName, description } = await c.req.json()

      const client = await getDbClient()
      try {
        // 检查角色是否存在且不是系统角色
        const checkResult = await client.queryObject(
          'SELECT is_system FROM sexup_roles WHERE id = $1',
          [roleId]
        )

        if (checkResult.rows.length === 0) {
          return c.json({ error: '角色不存在' }, 404)
        }

        const role = checkResult.rows[0] as any
        if (role.is_system) {
          return c.json({ error: '不能修改系统角色' }, 400)
        }

        // 更新角色
        const updates: string[] = []
        const params: any[] = []
        let paramIndex = 1

        if (displayName !== undefined) {
          updates.push(`display_name = $${paramIndex++}`)
          params.push(displayName)
        }

        if (description !== undefined) {
          updates.push(`description = $${paramIndex++}`)
          params.push(description)
        }

        if (updates.length === 0) {
          return c.json({ error: '没有要更新的字段' }, 400)
        }

        updates.push(`updated_at = NOW()`)
        params.push(roleId)

        const query = `
          UPDATE sexup_roles 
          SET ${updates.join(', ')}
          WHERE id = $${paramIndex}
          RETURNING id, name, display_name, description, updated_at
        `

        const result = await client.queryObject(query, params)

        return c.json({
          message: '角色更新成功',
          role: result.rows[0]
        })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('更新角色失败:', error)
      return c.json({ error: '更新角色失败' }, 500)
    }
  })

  // 删除角色
  app.delete('/api/v1/roles/:id', requirePermissions('roles:delete'), async (c: HonoContext) => {
    try {
      const roleId = parseInt(c.req.param('id'))

      const client = await getDbClient()
      try {
        // 检查角色是否存在且不是系统角色
        const checkResult = await client.queryObject(
          'SELECT is_system FROM sexup_roles WHERE id = $1',
          [roleId]
        )

        if (checkResult.rows.length === 0) {
          return c.json({ error: '角色不存在' }, 404)
        }

        const role = checkResult.rows[0] as any
        if (role.is_system) {
          return c.json({ error: '不能删除系统角色' }, 400)
        }

        // 检查是否有用户使用此角色
        const userCountResult = await client.queryObject(
          'SELECT COUNT(*) as count FROM sexup_user_roles WHERE role_id = $1',
          [roleId]
        )

        const userCount = Number((userCountResult.rows[0] as any).count)
        if (userCount > 0) {
          return c.json({ error: `无法删除角色，还有 ${userCount} 个用户使用此角色` }, 400)
        }

        // 删除角色
        await client.queryArray('DELETE FROM sexup_roles WHERE id = $1', [roleId])

        return c.json({ message: '角色删除成功' })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('删除角色失败:', error)
      return c.json({ error: '删除角色失败' }, 500)
    }
  })

  // 获取角色权限
  app.get('/api/v1/roles/:id/permissions', requirePermissions('roles:read'), async (c: HonoContext) => {
    try {
      const roleId = parseInt(c.req.param('id'))

      const client = await getDbClient()
      try {
        const result = await client.queryObject(`
          SELECT p.id, p.name, p.display_name, p.description, p.resource, p.action
          FROM sexup_permissions p
          INNER JOIN sexup_role_permissions rp ON p.id = rp.permission_id
          WHERE rp.role_id = $1
          ORDER BY p.resource, p.action
        `, [roleId])

        return c.json({ permissions: result.rows })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('获取角色权限失败:', error)
      return c.json({ error: '获取角色权限失败' }, 500)
    }
  })

  // 分配角色权限
  app.post('/api/v1/roles/:id/permissions', requirePermissions('roles:update'), async (c: HonoContext) => {
    try {
      const roleId = parseInt(c.req.param('id'))
      const { permissionIds } = await c.req.json()

      if (!Array.isArray(permissionIds) || permissionIds.length === 0) {
        return c.json({ error: '权限ID列表不能为空' }, 400)
      }

      const client = await getDbClient()
      try {
        // 检查角色是否存在且不是系统角色
        const roleResult = await client.queryObject(
          'SELECT is_system FROM sexup_roles WHERE id = $1',
          [roleId]
        )

        if (roleResult.rows.length === 0) {
          return c.json({ error: '角色不存在' }, 404)
        }

        const role = roleResult.rows[0] as any
        if (role.is_system) {
          return c.json({ error: '不能修改系统角色权限' }, 400)
        }

        // 清除现有权限
        await client.queryArray(
          'DELETE FROM sexup_role_permissions WHERE role_id = $1',
          [roleId]
        )

        // 分配新权限
        for (const permissionId of permissionIds) {
          await client.queryArray(`
            INSERT INTO sexup_role_permissions (role_id, permission_id)
            VALUES ($1, $2)
            ON CONFLICT (role_id, permission_id) DO NOTHING
          `, [roleId, permissionId])
        }

        return c.json({ message: '角色权限分配成功' })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('分配角色权限失败:', error)
      return c.json({ error: '分配角色权限失败' }, 500)
    }
  })
}
