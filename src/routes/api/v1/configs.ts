import { Hono } from 'hono'
import type { HonoContext } from '../../../types/index.ts'
import { getDbClient } from '../../../utils/database.ts'
import { requirePermissions } from '../../../utils/rbac.ts'

/**
 * RESTful Configs API
 * 
 * GET    /api/v1/configs/evaluation-styles      - 获取评估风格列表
 * GET    /api/v1/configs/evaluation-perspectives - 获取评估视角列表
 * GET    /api/v1/configs/evaluation-details      - 获取详细程度列表
 * GET    /api/v1/configs/api-configs             - 获取用户API配置
 * POST   /api/v1/configs/api-configs             - 创建API配置
 * PUT    /api/v1/configs/api-configs/:id         - 更新API配置
 * DELETE /api/v1/configs/api-configs/:id         - 删除API配置
 */

export function setupConfigsRoutes(app: Hono) {
  
  // 获取评估风格列表
  app.get('/api/v1/configs/evaluation-styles', async (c: HonoContext) => {
    try {
      const client = await getDbClient()
      try {
        const result = await client.queryObject(`
          SELECT id, code, name, description, icon, color, sort_order
          FROM sexup_evaluation_styles
          WHERE is_active = true
          ORDER BY sort_order ASC
        `)

        // 转换BigInt为字符串
        const styles = result.rows.map(row => ({
          ...(row as any),
          id: String((row as any).id),
          sort_order: Number((row as any).sort_order)
        }))

        return c.json({ styles })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('获取评估风格失败:', error)
      return c.json({ error: '获取评估风格失败' }, 500)
    }
  })
  
  // 获取评估视角列表
  app.get('/api/v1/configs/evaluation-perspectives', async (c: HonoContext) => {
    try {
      const client = await getDbClient()
      try {
        const result = await client.queryObject(`
          SELECT id, code, name, description, icon, color, sort_order
          FROM sexup_evaluation_perspectives
          WHERE is_active = true
          ORDER BY sort_order ASC
        `)

        // 转换BigInt为字符串
        const perspectives = result.rows.map(row => ({
          ...(row as any),
          id: String((row as any).id),
          sort_order: Number((row as any).sort_order)
        }))

        return c.json({ perspectives })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('获取评估视角失败:', error)
      return c.json({ error: '获取评估视角失败' }, 500)
    }
  })
  
  // 获取详细程度列表
  app.get('/api/v1/configs/evaluation-details', async (c: HonoContext) => {
    try {
      const client = await getDbClient()
      try {
        const result = await client.queryObject(`
          SELECT id, code, name, description, icon, sort_order
          FROM sexup_evaluation_details
          WHERE is_active = true
          ORDER BY sort_order ASC
        `)

        // 转换BigInt为字符串
        const details = result.rows.map(row => ({
          ...(row as any),
          id: String((row as any).id),
          sort_order: Number((row as any).sort_order)
        }))

        return c.json({ details })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('获取详细程度失败:', error)
      return c.json({ error: '获取详细程度失败' }, 500)
    }
  })
  
  // 获取所有评估配置
  app.get('/api/v1/configs/evaluation-options', async (c: HonoContext) => {
    try {
      const client = await getDbClient()
      try {
        const [stylesResult, perspectivesResult, detailsResult] = await Promise.all([
          client.queryObject(`
            SELECT id, code, name, description, icon, color, sort_order
            FROM sexup_evaluation_styles
            WHERE is_active = true
            ORDER BY sort_order ASC
          `),
          client.queryObject(`
            SELECT id, code, name, description, icon, color, sort_order
            FROM sexup_evaluation_perspectives
            WHERE is_active = true
            ORDER BY sort_order ASC
          `),
          client.queryObject(`
            SELECT id, code, name, description, icon, sort_order
            FROM sexup_evaluation_details
            WHERE is_active = true
            ORDER BY sort_order ASC
          `)
        ])

        // 转换BigInt为字符串
        const styles = stylesResult.rows.map(row => ({
          ...(row as any),
          id: String((row as any).id),
          sort_order: Number((row as any).sort_order)
        }))

        const perspectives = perspectivesResult.rows.map(row => ({
          ...(row as any),
          id: String((row as any).id),
          sort_order: Number((row as any).sort_order)
        }))

        const details = detailsResult.rows.map(row => ({
          ...(row as any),
          id: String((row as any).id),
          sort_order: Number((row as any).sort_order)
        }))

        return c.json({
          styles,
          perspectives,
          details
        })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('获取评估配置失败:', error)
      return c.json({ error: '获取评估配置失败' }, 500)
    }
  })
  
  // 获取用户API配置
  app.get('/api/v1/configs/api-configs', requirePermissions('evaluations:create'), async (c: HonoContext) => {
    try {
      const currentUser = c.get('user')
      if (!currentUser) {
        return c.json({ error: '未登录' }, 401)
      }
      
      const client = await getDbClient()
      try {
        const result = await client.queryObject(`
          SELECT id, name, base_url, model, is_default, is_active, created_at
          FROM sexup_api_configs
          WHERE user_id = $1 AND is_active = true
          ORDER BY is_default DESC, created_at DESC
        `, [currentUser.id])

        // 转换BigInt为字符串
        const configs = result.rows.map(row => ({
          ...(row as any),
          id: String((row as any).id),
          user_id: String((row as any).user_id)
        }))

        return c.json({ configs })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('获取API配置失败:', error)
      return c.json({ error: '获取API配置失败' }, 500)
    }
  })
  
  // 创建API配置
  app.post('/api/v1/configs/api-configs', requirePermissions('evaluations:create'), async (c: HonoContext) => {
    try {
      const currentUser = c.get('user')
      if (!currentUser) {
        return c.json({ error: '未登录' }, 401)
      }
      
      const { name, baseUrl, apiKey, model, isDefault } = await c.req.json()
      
      if (!name || !baseUrl || !apiKey || !model) {
        return c.json({ error: '配置信息不完整' }, 400)
      }
      
      const client = await getDbClient()
      try {
        // 如果设置为默认，先取消其他默认配置
        if (isDefault) {
          await client.queryArray(`
            UPDATE sexup_api_configs 
            SET is_default = false 
            WHERE user_id = $1
          `, [currentUser.id])
        }
        
        const result = await client.queryObject(`
          INSERT INTO sexup_api_configs (user_id, name, base_url, api_key, model, is_default)
          VALUES ($1, $2, $3, $4, $5, $6)
          RETURNING id, name, base_url, model, is_default, created_at
        `, [currentUser.id, name, baseUrl, apiKey, model, isDefault || false])

        // 转换BigInt为字符串
        const config = {
          ...(result.rows[0] as any),
          id: String((result.rows[0] as any).id)
        }

        return c.json({
          message: 'API配置创建成功',
          config
        }, 201)
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('创建API配置失败:', error)
      return c.json({ error: '创建API配置失败' }, 500)
    }
  })
  
  // 更新API配置
  app.put('/api/v1/configs/api-configs/:id', requirePermissions('evaluations:create'), async (c: HonoContext) => {
    try {
      const currentUser = c.get('user')
      if (!currentUser) {
        return c.json({ error: '未登录' }, 401)
      }
      
      const configId = parseInt(c.req.param('id'))
      const { name, baseUrl, apiKey, model, isDefault } = await c.req.json()
      
      const client = await getDbClient()
      try {
        // 检查配置是否属于当前用户
        const checkResult = await client.queryObject(
          'SELECT user_id FROM sexup_api_configs WHERE id = $1',
          [configId]
        )
        
        if (checkResult.rows.length === 0) {
          return c.json({ error: 'API配置不存在' }, 404)
        }
        
        const config = checkResult.rows[0] as any
        if (config.user_id !== currentUser.id) {
          return c.json({ error: '无权修改此配置' }, 403)
        }
        
        // 如果设置为默认，先取消其他默认配置
        if (isDefault) {
          await client.queryArray(`
            UPDATE sexup_api_configs 
            SET is_default = false 
            WHERE user_id = $1 AND id != $2
          `, [currentUser.id, configId])
        }
        
        // 更新配置
        const updates: string[] = []
        const params: any[] = []
        let paramIndex = 1
        
        if (name !== undefined) {
          updates.push(`name = $${paramIndex++}`)
          params.push(name)
        }
        
        if (baseUrl !== undefined) {
          updates.push(`base_url = $${paramIndex++}`)
          params.push(baseUrl)
        }
        
        if (apiKey !== undefined) {
          updates.push(`api_key = $${paramIndex++}`)
          params.push(apiKey)
        }
        
        if (model !== undefined) {
          updates.push(`model = $${paramIndex++}`)
          params.push(model)
        }
        
        if (isDefault !== undefined) {
          updates.push(`is_default = $${paramIndex++}`)
          params.push(isDefault)
        }
        
        if (updates.length === 0) {
          return c.json({ error: '没有要更新的字段' }, 400)
        }
        
        updates.push(`updated_at = NOW()`)
        params.push(configId)
        
        const query = `
          UPDATE sexup_api_configs 
          SET ${updates.join(', ')}
          WHERE id = $${paramIndex}
          RETURNING id, name, base_url, model, is_default, updated_at
        `
        
        const result = await client.queryObject(query, params)

        // 转换BigInt为字符串
        const updatedConfig = {
          ...(result.rows[0] as any),
          id: String((result.rows[0] as any).id)
        }

        return c.json({
          message: 'API配置更新成功',
          config: updatedConfig
        })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('更新API配置失败:', error)
      return c.json({ error: '更新API配置失败' }, 500)
    }
  })
  
  // 删除API配置
  app.delete('/api/v1/configs/api-configs/:id', requirePermissions('evaluations:create'), async (c: HonoContext) => {
    try {
      const currentUser = c.get('user')
      if (!currentUser) {
        return c.json({ error: '未登录' }, 401)
      }
      
      const configId = parseInt(c.req.param('id'))
      
      const client = await getDbClient()
      try {
        const result = await client.queryArray(`
          DELETE FROM sexup_api_configs 
          WHERE id = $1 AND user_id = $2
        `, [configId, currentUser.id])
        
        if (result.rowCount === 0) {
          return c.json({ error: 'API配置不存在或无权删除' }, 404)
        }
        
        return c.json({ message: 'API配置删除成功' })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('删除API配置失败:', error)
      return c.json({ error: '删除API配置失败' }, 500)
    }
  })
}
