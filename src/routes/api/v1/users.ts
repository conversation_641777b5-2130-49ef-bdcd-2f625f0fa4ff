import { Hono } from 'hono'
import type { HonoContext } from '../../../types/index.ts'
import { getDbClient } from '../../../utils/database.ts'
import { requirePermissions, getUserPermissions, assignUserRole, removeUserRole, checkUserPermission } from '../../../utils/rbac.ts'

/**
 * RESTful Users API
 * 
 * GET    /api/v1/users          - 获取用户列表
 * GET    /api/v1/users/:id      - 获取指定用户
 * PUT    /api/v1/users/:id      - 更新用户信息
 * DELETE /api/v1/users/:id      - 删除用户
 * GET    /api/v1/users/:id/roles - 获取用户角色
 * POST   /api/v1/users/:id/roles - 分配用户角色
 * DELETE /api/v1/users/:id/roles/:roleId - 移除用户角色
 */

export function setupUsersRoutes(app: Hono) {
  
  // 获取用户列表
  app.get('/api/v1/users', requirePermissions('users:list'), async (c: HonoContext) => {
    try {
      const page = parseInt(c.req.query('page') || '1')
      const limit = parseInt(c.req.query('limit') || '20')
      const search = c.req.query('search') || ''
      const offset = (page - 1) * limit
      
      const client = await getDbClient()
      try {
        let query = `
          SELECT u.id, u.username, u.email, u.is_active, u.created_at, u.last_login_at, u.updated_at,
                 r.name as default_role_name, r.display_name as default_role_display_name,
                 (SELECT COUNT(*) FROM sexup_evaluations WHERE user_id = u.id) as evaluation_count,
                 (SELECT COUNT(*) FROM sexup_shares WHERE user_id = u.id) as share_count
          FROM sexup_users u
          LEFT JOIN sexup_roles r ON u.default_role_id = r.id
        `
        const params: any[] = []
        
        if (search) {
          query += ` WHERE u.username ILIKE $1 OR u.email ILIKE $1`
          params.push(`%${search}%`)
        }
        
        query += ` ORDER BY u.created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`
        params.push(limit, offset)
        
        const result = await client.queryObject(query, params)
        
        // 获取总数
        let countQuery = 'SELECT COUNT(*) as total FROM sexup_users u'
        const countParams: any[] = []
        
        if (search) {
          countQuery += ` WHERE u.username ILIKE $1 OR u.email ILIKE $1`
          countParams.push(`%${search}%`)
        }
        
        const countResult = await client.queryObject(countQuery, countParams)
        const total = Number((countResult.rows[0] as any).total)
        
        return c.json({
          users: result.rows,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit)
          }
        })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('获取用户列表失败:', error)
      return c.json({ error: '获取用户列表失败' }, 500)
    }
  })
  
  // 获取指定用户
  app.get('/api/v1/users/:id', requirePermissions('users:read'), async (c: HonoContext) => {
    try {
      const userId = parseInt(c.req.param('id'))
      const currentUser = c.get('user') // 从中间件获取用户信息

      if (!currentUser) {
        return c.json({ error: '未登录' }, 401)
      }

      // 用户只能查看自己的信息，除非有管理权限
      if (currentUser.id !== userId) {
        const permissionCheck = await checkUserPermission(currentUser.id, 'users:list')
        if (!permissionCheck.allowed) {
          return c.json({ error: '权限不足' }, 403)
        }
      }
      
      const client = await getDbClient()
      try {
        const result = await client.queryObject(`
          SELECT u.id, u.username, u.email, u.is_active, u.created_at, u.last_login_at,
                 r.name as default_role_name, r.display_name as default_role_display_name
          FROM sexup_users u
          LEFT JOIN sexup_roles r ON u.default_role_id = r.id
          WHERE u.id = $1
        `, [userId])
        
        if (result.rows.length === 0) {
          return c.json({ error: '用户不存在' }, 404)
        }
        
        const user = result.rows[0]
        
        // 获取用户权限信息
        const permissions = await getUserPermissions(userId)
        
        return c.json({
          user: {
            ...(user as any),
            roles: permissions.roles,
            permissions: permissions.permissions
          }
        })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return c.json({ error: '获取用户信息失败' }, 500)
    }
  })
  
  // 更新用户信息
  app.put('/api/v1/users/:id', async (c: HonoContext) => {
    try {
      const userId = parseInt(c.req.param('id'))
      const currentUser = c.get('user')
      const { email, isActive } = await c.req.json()

      if (!currentUser) {
        return c.json({ error: '未登录' }, 401)
      }

      // 用户只能更新自己的信息，除非有管理权限
      if (currentUser.id !== userId) {
        const permissionCheck = await checkUserPermission(currentUser.id, 'users:update')
        if (!permissionCheck.allowed) {
          return c.json({ error: '权限不足' }, 403)
        }
      }
      
      const client = await getDbClient()
      try {
        const updates: string[] = []
        const params: any[] = []
        let paramIndex = 1
        
        if (email !== undefined) {
          updates.push(`email = $${paramIndex++}`)
          params.push(email)
        }
        
        if (isActive !== undefined && currentUser.id !== userId) {
          // 只有管理员可以修改激活状态
          updates.push(`is_active = $${paramIndex++}`)
          params.push(isActive)
        }
        
        if (updates.length === 0) {
          return c.json({ error: '没有要更新的字段' }, 400)
        }
        
        updates.push(`updated_at = NOW()`)
        params.push(userId)
        
        const query = `
          UPDATE sexup_users 
          SET ${updates.join(', ')}
          WHERE id = $${paramIndex}
          RETURNING id, username, email, is_active, updated_at
        `
        
        const result = await client.queryObject(query, params)
        
        if (result.rows.length === 0) {
          return c.json({ error: '用户不存在' }, 404)
        }
        
        return c.json({ 
          message: '用户信息更新成功',
          user: result.rows[0]
        })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('更新用户信息失败:', error)
      return c.json({ error: '更新用户信息失败' }, 500)
    }
  })
  
  // 删除用户
  app.delete('/api/v1/users/:id', requirePermissions('users:delete'), async (c: HonoContext) => {
    try {
      const userId = parseInt(c.req.param('id'))
      const currentUser = c.get('user')

      if (!currentUser) {
        return c.json({ error: '未登录' }, 401)
      }

      // 不能删除自己
      if (currentUser.id === userId) {
        return c.json({ error: '不能删除自己的账户' }, 400)
      }
      
      const client = await getDbClient()
      try {
        const result = await client.queryArray(
          'DELETE FROM sexup_users WHERE id = $1',
          [userId]
        )
        
        if (result.rowCount === 0) {
          return c.json({ error: '用户不存在' }, 404)
        }
        
        return c.json({ message: '用户删除成功' })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('删除用户失败:', error)
      return c.json({ error: '删除用户失败' }, 500)
    }
  })
  
  // 获取用户角色
  app.get('/api/v1/users/:id/roles', requirePermissions('users:read'), async (c: HonoContext) => {
    try {
      const userId = parseInt(c.req.param('id'))
      const permissions = await getUserPermissions(userId)
      
      return c.json({ roles: permissions.roles })
    } catch (error) {
      console.error('获取用户角色失败:', error)
      return c.json({ error: '获取用户角色失败' }, 500)
    }
  })
  
  // 分配用户角色
  app.post('/api/v1/users/:id/roles', requirePermissions('roles:assign'), async (c: HonoContext) => {
    try {
      const userId = parseInt(c.req.param('id'))
      const currentUser = c.get('user')
      const { roleId, roleName, expiresAt } = await c.req.json()

      if (!currentUser) {
        return c.json({ error: '未登录' }, 401)
      }

      // 支持通过roleId或roleName分配角色
      let targetRoleName = roleName
      if (roleId && !roleName) {
        // 通过roleId查找roleName
        const client = await getDbClient()
        try {
          const roleResult = await client.queryObject('SELECT name FROM sexup_roles WHERE id = $1', [roleId])
          if (roleResult.rows.length === 0) {
            return c.json({ error: '角色不存在' }, 404)
          }
          targetRoleName = (roleResult.rows[0] as any).name
        } finally {
          await client.end()
        }
      }

      if (!targetRoleName) {
        return c.json({ error: '角色名称或ID不能为空' }, 400)
      }

      const expirationDate = expiresAt ? new Date(expiresAt) : undefined
      const success = await assignUserRole(userId, targetRoleName, currentUser.id, expirationDate)
      
      if (!success) {
        return c.json({ error: '角色分配失败' }, 500)
      }
      
      return c.json({ message: '角色分配成功' })
    } catch (error) {
      console.error('分配用户角色失败:', error)
      return c.json({ error: '分配用户角色失败' }, 500)
    }
  })
  
  // 移除用户角色
  app.delete('/api/v1/users/:id/roles/:roleId', requirePermissions('roles:assign'), async (c: HonoContext) => {
    try {
      const userId = parseInt(c.req.param('id'))
      const roleIdParam = c.req.param('roleId')

      // 支持通过roleId或roleName移除角色
      let targetRoleName = roleIdParam

      // 如果参数是数字，则认为是roleId，需要查找roleName
      if (/^\d+$/.test(roleIdParam)) {
        const client = await getDbClient()
        try {
          const roleResult = await client.queryObject('SELECT name FROM sexup_roles WHERE id = $1', [parseInt(roleIdParam)])
          if (roleResult.rows.length === 0) {
            return c.json({ error: '角色不存在' }, 404)
          }
          targetRoleName = (roleResult.rows[0] as any).name
        } finally {
          await client.end()
        }
      }

      const success = await removeUserRole(userId, targetRoleName)
      
      if (!success) {
        return c.json({ error: '角色移除失败' }, 500)
      }
      
      return c.json({ message: '角色移除成功' })
    } catch (error) {
      console.error('移除用户角色失败:', error)
      return c.json({ error: '移除用户角色失败' }, 500)
    }
  })
}
