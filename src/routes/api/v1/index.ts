import { Hono } from 'hono'
import type { HonoContext } from '../../../types/index.ts'
import { getCurrentUser } from '../../../utils/auth.ts'
import { setupAuthRoutes } from './auth.ts'
import { setupUsersRoutes } from './users.ts'
import { setupEvaluationsRoutes } from './evaluations.ts'
import { setupSharesRoutes } from './shares.ts'
import { setupRolesRoutes } from './roles.ts'
import { setupPermissionsRoutes } from './permissions.ts'
import { setupConfigsRoutes } from './configs.ts'
import { setupUploadRoutes } from './upload.ts'

/**
 * RESTful API v1 路由
 * 
 * 统一的API版本管理和中间件配置
 */

// 认证中间件
async function authMiddleware(c: any, next: any) {
  const user = await getCurrentUser(c)
  
  // 将用户信息存储到上下文中，供后续中间件和路由使用
  c.set('user', user)
  
  await next()
}

// API版本信息中间件
async function apiVersionMiddleware(c: any, next: any) {
  c.header('API-Version', 'v1')
  c.header('X-Powered-By', 'Hono + Deno + RBAC')
  await next()
}

// 错误处理中间件
async function errorHandlerMiddleware(c: any, next: any) {
  try {
    await next()
  } catch (error) {
    console.error('API错误:', error)
    
    // 根据错误类型返回不同的状态码
    const errorMessage = error instanceof Error ? error.message : String(error)
    if (errorMessage.includes('权限')) {
      return c.json({ error: '权限不足' }, 403)
    } else if (errorMessage.includes('未登录')) {
      return c.json({ error: '未登录' }, 401)
    } else if (errorMessage.includes('不存在')) {
      return c.json({ error: '资源不存在' }, 404)
    } else {
      return c.json({ 
        error: '服务器内部错误',
        details: error instanceof Error ? error.message : '未知错误'
      }, 500)
    }
  }
}

export function setupV1ApiRoutes(app: Hono) {

  // 应用全局中间件
  app.use('/api/v1/*', apiVersionMiddleware)
  app.use('/api/v1/*', errorHandlerMiddleware)

  // 认证中间件（跳过认证相关端点）
  app.use('/api/v1/*', async (c, next) => {
    const path = c.req.path
    console.log('API路径检查:', path)

    // 跳过认证端点和公开端点，但status端点需要认证信息
    if (path.startsWith('/api/v1/auth/') ||
        path === '/api/v1/docs' ||
        path.startsWith('/api/v1/shares/public')) {
      console.log('跳过认证检查:', path)
      await next()
    } else {
      console.log('需要认证检查:', path)
      await authMiddleware(c, next)
    }
  })
  
  // API状态检查
  app.get('/api/v1/status', (c: HonoContext) => {
    const user = c.get('user')
    
    return c.json({
      status: 'running',
      version: 'v1',
      timestamp: new Date().toISOString(),
      authenticated: !!user,
      user: user ? {
        id: user.id,
        username: user.username
      } : null
    })
  })
  
  // API文档信息
  app.get('/api/v1/docs', (c: HonoContext) => {
    return c.json({
      title: 'SexUp AI Evaluation API v1',
      description: 'RESTful API with RBAC permission system',
      version: '1.0.0',
      endpoints: {
        auth: {
          'POST /api/v1/auth/register': '用户注册',
          'POST /api/v1/auth/login': '用户登录',
          'GET /api/v1/auth/me': '获取当前用户信息',
          'POST /api/v1/auth/logout': '用户登出',
          'POST /api/v1/auth/refresh': '刷新token'
        },
        users: {
          'GET /api/v1/users': '获取用户列表',
          'GET /api/v1/users/:id': '获取指定用户',
          'PUT /api/v1/users/:id': '更新用户信息',
          'DELETE /api/v1/users/:id': '删除用户',
          'GET /api/v1/users/:id/roles': '获取用户角色',
          'POST /api/v1/users/:id/roles': '分配用户角色',
          'DELETE /api/v1/users/:id/roles/:roleId': '移除用户角色'
        },
        evaluations: {
          'POST /api/v1/evaluations': '创建评估',
          'POST /api/v1/evaluations/save': '保存已有评估结果',
          'GET /api/v1/evaluations': '获取评估列表',
          'GET /api/v1/evaluations/:id': '获取指定评估',
          'PUT /api/v1/evaluations/:id': '更新评估',
          'DELETE /api/v1/evaluations/:id': '删除评估',
          'DELETE /api/v1/evaluations/batch': '批量删除评估'
        },
        upload: {
          'POST /api/v1/upload': '上传单个文件到CDN',
          'POST /api/v1/upload/batch': '批量上传文件到CDN'
        },
        shares: {
          'POST /api/v1/shares': '创建分享',
          'GET /api/v1/shares': '获取分享列表',
          'GET /api/v1/shares/:id': '获取指定分享',
          'PUT /api/v1/shares/:id': '更新分享',
          'DELETE /api/v1/shares/:id': '删除分享',
          'GET /api/v1/shares/public': '获取公开分享列表'
        },
        roles: {
          'GET /api/v1/roles': '获取角色列表',
          'GET /api/v1/roles/:id': '获取指定角色',
          'POST /api/v1/roles': '创建角色',
          'PUT /api/v1/roles/:id': '更新角色',
          'DELETE /api/v1/roles/:id': '删除角色',
          'GET /api/v1/roles/:id/permissions': '获取角色权限',
          'POST /api/v1/roles/:id/permissions': '分配角色权限'
        },
        permissions: {
          'GET /api/v1/permissions': '获取权限列表',
          'GET /api/v1/permissions/:id': '获取指定权限',
          'POST /api/v1/permissions': '创建权限',
          'PUT /api/v1/permissions/:id': '更新权限',
          'DELETE /api/v1/permissions/:id': '删除权限'
        }
      },
      authentication: {
        type: 'Bearer Token',
        header: 'Authorization: Bearer <token>',
        description: '使用JWT token进行身份验证'
      },
      permissions: {
        description: '基于RBAC的权限控制系统',
        roles: [
          'super_admin - 超级管理员',
          'admin - 管理员',
          'moderator - 版主',
          'vip_user - VIP用户',
          'regular_user - 普通用户',
          'guest - 访客'
        ]
      }
    })
  })
  
  // 设置各个资源的路由
  setupAuthRoutes(app)
  setupUsersRoutes(app)
  setupEvaluationsRoutes(app)
  setupSharesRoutes(app)
  setupRolesRoutes(app)
  setupPermissionsRoutes(app)
  setupConfigsRoutes(app)
  setupUploadRoutes(app)
  
  // 404处理
  app.all('/api/v1/*', (c: HonoContext) => {
    return c.json({ 
      error: 'API端点不存在',
      path: c.req.path,
      method: c.req.method,
      availableEndpoints: '/api/v1/docs'
    }, 404)
  })
}
