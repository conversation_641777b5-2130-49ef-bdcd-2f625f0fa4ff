import { Hono } from 'hono'
import type { HonoContext } from '../../../types/index.ts'
import { getDbClient } from '../../../utils/database.ts'
import { getCurrentUser } from '../../../utils/auth.ts'
import { requirePermissions } from '../../../utils/rbac.ts'
import { generateShareId } from '../../../utils/helpers.ts'

/**
 * RESTful Shares API
 * 
 * POST   /api/v1/shares        - 创建分享
 * GET    /api/v1/shares        - 获取分享列表
 * GET    /api/v1/shares/:id    - 获取指定分享
 * PUT    /api/v1/shares/:id    - 更新分享
 * DELETE /api/v1/shares/:id    - 删除分享
 * GET    /api/v1/shares/public - 获取公开分享列表
 */

export function setupSharesRoutes(app: Hono) {

  // 获取公开分享列表 - 必须在其他shares路由之前注册
  app.get('/api/v1/shares/public', async (c: HonoContext) => {
    try {
      console.log('公开分享API被调用')

      // 检查用户是否登录
      const user = c.get('user')
      const isLoggedIn = !!user
      console.log('用户登录状态:', isLoggedIn, user ? `用户ID: ${user.id}` : '未登录')

      const page = parseInt(c.req.query('page') || '1')
      const requestedLimit = parseInt(c.req.query('limit') || '10')

      // 未登录用户强制限制为10条，登录用户可以自定义
      const limit = isLoggedIn ? Math.min(requestedLimit, 50) : 10
      console.log(`请求limit: ${requestedLimit}, 实际limit: ${limit}, 登录状态: ${isLoggedIn}`)

      const offset = (page - 1) * limit

      const client = await getDbClient()
      try {
        const result = await client.queryObject(`
          SELECT s.id, s.data, s.created_at, s.view_count, u.username as creator_username
          FROM sexup_shares s
          LEFT JOIN sexup_users u ON s.user_id = u.id
          WHERE s.is_public = true AND s.expires_at > NOW()
          ORDER BY s.created_at DESC
          LIMIT $1 OFFSET $2
        `, [limit, offset])

        const shares = result.rows.map((row: any) => ({
          id: row.id,
          data: row.data,
          createdAt: row.created_at,
          viewCount: row.view_count || 0,
          shareUrl: `/s/${row.id}`,
          creatorUsername: row.creator_username
        }))

        // 获取总数
        const countResult = await client.queryObject(
          'SELECT COUNT(*) as total FROM sexup_shares WHERE is_public = true AND expires_at > NOW()'
        )
        const total = Number((countResult.rows[0] as any).total)

        // 对于未登录用户，限制显示的总数和页数
        let displayTotal = total
        let displayTotalPages = Math.ceil(total / limit)

        if (!isLoggedIn) {
          // 未登录用户最多只能看到10条记录（1页）
          displayTotal = Math.min(total, 10)
          displayTotalPages = 1
        }

        console.log(`公开分享查询成功: ${shares.length} 条记录, 总数: ${total}, 显示总数: ${displayTotal}`)
        return c.json({
          shares,
          pagination: {
            page,
            limit,
            total: displayTotal,
            totalPages: displayTotalPages,
            hasMore: isLoggedIn ? (page < displayTotalPages) : false
          }
        })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('获取公开分享失败:', error)
      return c.json({ error: '获取公开分享失败' }, 500)
    }
  })

  // 创建分享
  app.post('/api/v1/shares', requirePermissions('shares:create'), async (c: HonoContext) => {
    try {
      const currentUser = await getCurrentUser(c)
      const shareData = await c.req.json()
      
      if (!shareData.verdict || !shareData.rating || !shareData.explanation) {
        return c.json({ error: '分享数据不完整' }, 400)
      }

      const isPublic = shareData.isPublic || false
      const shareId = generateShareId()
      
      // 设置过期时间（30天）
      const expiresAt = new Date()
      expiresAt.setDate(expiresAt.getDate() + 30)

      const client = await getDbClient()
      try {
        await client.queryArray(
          'INSERT INTO sexup_shares (id, user_id, data, is_public, expires_at) VALUES ($1, $2, $3, $4, $5)',
          [shareId, currentUser?.id, JSON.stringify(shareData), isPublic, expiresAt]
        )

        return c.json({
          id: shareId,
          shareUrl: `/s/${shareId}`,
          fullUrl: `${c.req.url.split('/api')[0]}/s/${shareId}`,
          expiresAt: expiresAt.toISOString(),
          isPublic
        }, 201)
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('创建分享失败:', error)
      return c.json({ error: '创建分享失败，请稍后重试' }, 500)
    }
  })

  // 获取用户的分享列表
  app.get('/api/v1/shares', requirePermissions('shares:list'), async (c: HonoContext) => {
    try {
      const currentUser = await getCurrentUser(c)
      const page = parseInt(c.req.query('page') || '1')
      const limit = parseInt(c.req.query('limit') || '20')
      const offset = (page - 1) * limit

      const client = await getDbClient()
      try {
        const result = await client.queryObject(`
          SELECT id, data, is_public, created_at, expires_at, view_count 
          FROM sexup_shares 
          WHERE user_id = $1 AND expires_at > NOW() 
          ORDER BY created_at DESC 
          LIMIT $2 OFFSET $3
        `, [currentUser?.id, limit, offset])

        const shares = result.rows.map((row: any) => ({
          id: row.id,
          data: row.data,
          isPublic: row.is_public,
          createdAt: row.created_at,
          expiresAt: row.expires_at,
          viewCount: row.view_count || 0,
          shareUrl: `/s/${row.id}`
        }))

        // 获取总数
        const countResult = await client.queryObject(
          'SELECT COUNT(*) as total FROM sexup_shares WHERE user_id = $1 AND expires_at > NOW()',
          [currentUser?.id]
        )
        const total = Number((countResult.rows[0] as any).total)

        return c.json({ 
          shares,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit)
          }
        })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('获取分享列表失败:', error)
      return c.json({ error: '获取分享列表失败' }, 500)
    }
  })

  // 获取指定分享
  app.get('/api/v1/shares/:id', requirePermissions('shares:read'), async (c: HonoContext) => {
    try {
      const shareId = c.req.param('id')
      const currentUser = await getCurrentUser(c)

      const client = await getDbClient()
      try {
        const result = await client.queryObject(`
          SELECT s.*, u.username as creator_username
          FROM sexup_shares s
          LEFT JOIN sexup_users u ON s.user_id = u.id
          WHERE s.id = $1 AND s.expires_at > NOW()
        `, [shareId])

        if (result.rows.length === 0) {
          return c.json({ error: '分享不存在或已过期' }, 404)
        }

        const share = result.rows[0] as any

        // 检查访问权限：公开分享或创建者可以查看
        if (!share.is_public && share.user_id !== currentUser?.id) {
          return c.json({ error: '无权访问此分享' }, 403)
        }

        // 增加查看次数
        await client.queryArray(
          'UPDATE sexup_shares SET view_count = view_count + 1 WHERE id = $1',
          [shareId]
        )

        return c.json({
          share: {
            id: share.id,
            data: share.data,
            isPublic: share.is_public,
            createdAt: share.created_at,
            expiresAt: share.expires_at,
            viewCount: (share.view_count || 0) + 1,
            creatorUsername: share.creator_username
          }
        })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('获取分享失败:', error)
      return c.json({ error: '获取分享失败' }, 500)
    }
  })

  // 更新分享
  app.put('/api/v1/shares/:id', requirePermissions('shares:update'), async (c: HonoContext) => {
    try {
      const shareId = c.req.param('id')
      const currentUser = await getCurrentUser(c)
      const { isPublic } = await c.req.json()

      const client = await getDbClient()
      try {
        const result = await client.queryArray(`
          UPDATE sexup_shares 
          SET is_public = $1 
          WHERE id = $2 AND user_id = $3
        `, [isPublic, shareId, currentUser?.id])

        if (result.rowCount === 0) {
          return c.json({ error: '分享不存在或无权修改' }, 404)
        }

        return c.json({ message: '分享更新成功' })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('更新分享失败:', error)
      return c.json({ error: '更新失败，请稍后重试' }, 500)
    }
  })

  // 删除分享
  app.delete('/api/v1/shares/:id', requirePermissions('shares:delete'), async (c: HonoContext) => {
    try {
      const shareId = c.req.param('id')
      const currentUser = await getCurrentUser(c)

      const client = await getDbClient()
      try {
        const result = await client.queryArray(`
          DELETE FROM sexup_shares 
          WHERE id = $1 AND user_id = $2
        `, [shareId, currentUser?.id])

        if (result.rowCount === 0) {
          return c.json({ error: '分享不存在或无权删除' }, 404)
        }

        return c.json({ message: '分享删除成功' })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('删除分享失败:', error)
      return c.json({ error: '删除失败，请稍后重试' }, 500)
    }
  })
}
