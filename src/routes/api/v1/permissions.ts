import { Hono } from 'hono'
import type { HonoContext } from '../../../types/index.ts'
import { getDbClient } from '../../../utils/database.ts'
import { requirePermissions } from '../../../utils/rbac.ts'

/**
 * RESTful Permissions API
 * 
 * GET    /api/v1/permissions     - 获取权限列表
 * GET    /api/v1/permissions/:id - 获取指定权限
 * POST   /api/v1/permissions     - 创建权限
 * PUT    /api/v1/permissions/:id - 更新权限
 * DELETE /api/v1/permissions/:id - 删除权限
 */

export function setupPermissionsRoutes(app: Hono) {
  
  // 获取权限列表
  app.get('/api/v1/permissions', requirePermissions('system:admin'), async (c: HonoContext) => {
    try {
      const page = parseInt(c.req.query('page') || '1')
      const limit = parseInt(c.req.query('limit') || '50')
      const resource = c.req.query('resource') // 可选：按资源筛选
      const offset = (page - 1) * limit

      const client = await getDbClient()
      try {
        let query = `
          SELECT p.id, p.name, p.display_name, p.description, p.resource, p.action, p.created_at,
                 COUNT(rp.role_id) as role_count
          FROM sexup_permissions p
          LEFT JOIN sexup_role_permissions rp ON p.id = rp.permission_id
        `
        const params: any[] = []

        if (resource) {
          query += ` WHERE p.resource = $${params.length + 1}`
          params.push(resource)
        }

        query += ` GROUP BY p.id, p.name, p.display_name, p.description, p.resource, p.action, p.created_at`
        query += ` ORDER BY p.resource, p.action`
        query += ` LIMIT $${params.length + 1} OFFSET $${params.length + 2}`
        params.push(limit, offset)

        const result = await client.queryObject(query, params)

        // 获取总数
        let countQuery = 'SELECT COUNT(*) as total FROM sexup_permissions p'
        const countParams: any[] = []

        if (resource) {
          countQuery += ` WHERE p.resource = $1`
          countParams.push(resource)
        }

        const countResult = await client.queryObject(countQuery, countParams)
        const total = Number((countResult.rows[0] as any).total)

        // 获取所有资源类型
        const resourcesResult = await client.queryObject(
          'SELECT DISTINCT resource FROM sexup_permissions ORDER BY resource'
        )
        const resources = resourcesResult.rows.map((row: any) => row.resource)

        return c.json({
          permissions: result.rows,
          resources,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit)
          }
        })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('获取权限列表失败:', error)
      return c.json({ error: '获取权限列表失败' }, 500)
    }
  })

  // 获取指定权限
  app.get('/api/v1/permissions/:id', requirePermissions('system:admin'), async (c: HonoContext) => {
    try {
      const permissionId = parseInt(c.req.param('id'))

      const client = await getDbClient()
      try {
        const result = await client.queryObject(`
          SELECT p.*, COUNT(rp.role_id) as role_count
          FROM sexup_permissions p
          LEFT JOIN sexup_role_permissions rp ON p.id = rp.permission_id
          WHERE p.id = $1
          GROUP BY p.id
        `, [permissionId])

        if (result.rows.length === 0) {
          return c.json({ error: '权限不存在' }, 404)
        }

        const permission = result.rows[0]

        // 获取使用此权限的角色
        const rolesResult = await client.queryObject(`
          SELECT r.id, r.name, r.display_name
          FROM sexup_roles r
          INNER JOIN sexup_role_permissions rp ON r.id = rp.role_id
          WHERE rp.permission_id = $1
          ORDER BY r.name
        `, [permissionId])

        return c.json({
          permission: {
            ...(permission as any),
            roles: rolesResult.rows
          }
        })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('获取权限信息失败:', error)
      return c.json({ error: '获取权限信息失败' }, 500)
    }
  })

  // 创建权限
  app.post('/api/v1/permissions', requirePermissions('system:admin'), async (c: HonoContext) => {
    try {
      const { name, displayName, description, resource, action } = await c.req.json()

      if (!name || !displayName || !resource || !action) {
        return c.json({ error: '权限名称、显示名称、资源和操作不能为空' }, 400)
      }

      // 验证权限名称格式 (resource:action)
      const expectedName = `${resource}:${action}`
      if (name !== expectedName) {
        return c.json({ error: `权限名称应为 ${expectedName}` }, 400)
      }

      // 验证格式
      if (!/^[a-z_]+$/.test(resource) || !/^[a-z_]+$/.test(action)) {
        return c.json({ error: '资源和操作只能包含小写字母和下划线' }, 400)
      }

      const client = await getDbClient()
      try {
        const result = await client.queryObject(`
          INSERT INTO sexup_permissions (name, display_name, description, resource, action)
          VALUES ($1, $2, $3, $4, $5)
          RETURNING id, name, display_name, description, resource, action, created_at
        `, [name, displayName, description || '', resource, action])

        return c.json({
          message: '权限创建成功',
          permission: result.rows[0]
        }, 201)
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('创建权限失败:', error)
      if (error instanceof Error && error.message?.includes('duplicate key')) {
        return c.json({ error: '权限名称已存在' }, 400)
      }
      return c.json({ error: '创建权限失败' }, 500)
    }
  })

  // 更新权限
  app.put('/api/v1/permissions/:id', requirePermissions('system:admin'), async (c: HonoContext) => {
    try {
      const permissionId = parseInt(c.req.param('id'))
      const { displayName, description } = await c.req.json()

      const client = await getDbClient()
      try {
        // 检查权限是否存在
        const checkResult = await client.queryObject(
          'SELECT id FROM sexup_permissions WHERE id = $1',
          [permissionId]
        )

        if (checkResult.rows.length === 0) {
          return c.json({ error: '权限不存在' }, 404)
        }

        // 更新权限
        const updates: string[] = []
        const params: any[] = []
        let paramIndex = 1

        if (displayName !== undefined) {
          updates.push(`display_name = $${paramIndex++}`)
          params.push(displayName)
        }

        if (description !== undefined) {
          updates.push(`description = $${paramIndex++}`)
          params.push(description)
        }

        if (updates.length === 0) {
          return c.json({ error: '没有要更新的字段' }, 400)
        }

        params.push(permissionId)

        const query = `
          UPDATE sexup_permissions 
          SET ${updates.join(', ')}
          WHERE id = $${paramIndex}
          RETURNING id, name, display_name, description, resource, action
        `

        const result = await client.queryObject(query, params)

        return c.json({
          message: '权限更新成功',
          permission: result.rows[0]
        })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('更新权限失败:', error)
      return c.json({ error: '更新权限失败' }, 500)
    }
  })

  // 删除权限
  app.delete('/api/v1/permissions/:id', requirePermissions('system:admin'), async (c: HonoContext) => {
    try {
      const permissionId = parseInt(c.req.param('id'))

      const client = await getDbClient()
      try {
        // 检查是否有角色使用此权限
        const roleCountResult = await client.queryObject(
          'SELECT COUNT(*) as count FROM sexup_role_permissions WHERE permission_id = $1',
          [permissionId]
        )

        const roleCount = Number((roleCountResult.rows[0] as any).count)
        if (roleCount > 0) {
          return c.json({ error: `无法删除权限，还有 ${roleCount} 个角色使用此权限` }, 400)
        }

        // 删除权限
        const result = await client.queryArray(
          'DELETE FROM sexup_permissions WHERE id = $1',
          [permissionId]
        )

        if (result.rowCount === 0) {
          return c.json({ error: '权限不存在' }, 404)
        }

        return c.json({ message: '权限删除成功' })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('删除权限失败:', error)
      return c.json({ error: '删除权限失败' }, 500)
    }
  })
}
