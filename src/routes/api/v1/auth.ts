import { Hono } from 'hono'
import type { HonoContext } from '../../../types/index.ts'
import { getDbClient } from '../../../utils/database.ts'
import { hashPassword, verifyPassword, generateJwtToken, getCurrentUser, initializeNewUserPermissions } from '../../../utils/auth.ts'

// 发送重置密码邮件的函数
async function sendResetPasswordEmail(email: string, username: string, resetUrl: string, expires: Date): Promise<{success: boolean, message?: string}> {
  try {
    const emailTitle = '重置密码 - AI图片评估助手'
    const emailContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
          <h1 style="margin: 0; font-size: 28px;">🔐 重置密码</h1>
          <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">AI图片评估助手</p>
        </div>
        
        <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
          <p style="font-size: 16px; color: #333; margin-bottom: 20px;">
            您好 <strong>${username}</strong>，
          </p>
          
          <p style="font-size: 14px; color: #666; line-height: 1.6; margin-bottom: 25px;">
            我们收到了您的密码重置请求。请点击下面的按钮来重置您的密码：
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" 
               style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                      color: white; 
                      padding: 15px 30px; 
                      text-decoration: none; 
                      border-radius: 25px; 
                      font-size: 16px; 
                      font-weight: bold;
                      display: inline-block;
                      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);">
              重置我的密码
            </a>
          </div>
          
          <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 25px 0;">
            <p style="font-size: 13px; color: #856404; margin: 0; line-height: 1.5;">
              <strong>⚠️ 安全提示：</strong><br>
              • 此链接将在 <strong>${expires.toLocaleString('zh-CN')}</strong> 过期<br>
              • 如果您没有请求重置密码，请忽略此邮件<br>
              • 请勿将此链接分享给任何人
            </p>
          </div>
          
          <p style="font-size: 12px; color: #999; margin-top: 25px; line-height: 1.5;">
            如果按钮无法点击，请复制以下链接到浏览器地址栏：<br>
            <span style="word-break: break-all; background: #f1f1f1; padding: 5px; border-radius: 3px;">${resetUrl}</span>
          </p>
        </div>
        
        <div style="text-align: center; margin-top: 20px; padding: 15px;">
          <p style="font-size: 12px; color: #999; margin: 0;">
            © 2025 AI图片评估助手 | 十年磨一剑 自豪的采用 Deno 和 CockroachLabs 驱动
          </p>
        </div>
      </div>
    `

    const response = await fetch('https://push.h7ml.cn/forward', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        title: emailTitle,
        desp: emailContent,
        type: 'CustomEmail',
        config: {
          EMAIL_TYPE: 'html',
          EMAIL_TO_ADDRESS: email,
          EMAIL_AUTH_USER: '<EMAIL>',
          EMAIL_AUTH_PASS: 'udhgshpmjhopebbe',
          EMAIL_HOST: 'smtp.qq.com',
          EMAIL_PORT: 465,
          $EMAIL_TYPE: 'HTML'
        },
        option: {}
      })
    })

    const result = await response.json()
    
    if (response.ok && result.message === 'OK') {
      return { success: true }
    } else {
      return { success: false, message: result.message || '邮件发送失败' }
    }
  } catch (error) {
    console.error('邮件发送异常:', error)
    return { success: false, message: '邮件发送异常' }
  }
}

/**
 * RESTful Auth API v1
 * 
 * POST   /api/v1/auth/register - 用户注册
 * POST   /api/v1/auth/login    - 用户登录
 * GET    /api/v1/auth/me       - 获取当前用户信息
 * POST   /api/v1/auth/logout   - 用户登出
 * POST   /api/v1/auth/refresh  - 刷新token
 */

export function setupAuthRoutes(app: Hono) {
  
  // 用户注册
  app.post('/api/v1/auth/register', async (c: HonoContext) => {
    try {
      const { username, email, password } = await c.req.json()

      if (!username || !email || !password) {
        return c.json({ error: '请填写所有必填字段' }, 400)
      }

      // 验证用户名格式
      if (!/^[a-zA-Z0-9_]{3,20}$/.test(username)) {
        return c.json({ error: '用户名只能包含字母、数字和下划线，长度3-20个字符' }, 400)
      }

      // 验证邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(email)) {
        return c.json({ error: '请输入有效的邮箱地址' }, 400)
      }

      const client = await getDbClient()
      try {
        // 检查用户名和邮箱是否已存在
        const existingUser = await client.queryObject(
          'SELECT id FROM sexup_users WHERE username = $1 OR email = $2',
          [username, email]
        )

        if (existingUser.rows.length > 0) {
          return c.json({ error: '用户名或邮箱已被使用' }, 400)
        }

        // 哈希密码
        const hashedPassword = await hashPassword(password)

        // 创建用户
        const result = await client.queryObject(
          'INSERT INTO sexup_users (username, email, password_hash) VALUES ($1, $2, $3) RETURNING id, username',
          [username, email, hashedPassword]
        )

        const user = result.rows[0] as any
        const userId = String(user.id) // 保持为字符串以支持BIGINT

        // 初始化用户权限
        await initializeNewUserPermissions(userId)

        const token = await generateJwtToken(userId, user.username)

        return c.json({
          message: '注册成功',
          token,
          user: {
            id: userId,
            username: user.username,
            email: email
          }
        }, 201)
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('注册错误:', error)
      return c.json({ error: '注册失败，请稍后重试' }, 500)
    }
  })

  // 用户登录
  app.post('/api/v1/auth/login', async (c: HonoContext) => {
    try {
      const { username, password } = await c.req.json()

      if (!username || !password) {
        return c.json({ error: '请输入用户名和密码' }, 400)
      }

      const client = await getDbClient()
      try {
        const result = await client.queryObject(
          'SELECT id, username, email, password_hash FROM sexup_users WHERE username = $1 OR email = $1',
          [username]
        )

        if (result.rows.length === 0) {
          return c.json({ error: '用户名或密码错误' }, 400)
        }

        const user = result.rows[0] as any
        const isValid = await verifyPassword(password, user.password_hash)

        if (!isValid) {
          return c.json({ error: '用户名或密码错误' }, 400)
        }

        // 更新最后登录时间
        await client.queryArray(
          'UPDATE sexup_users SET last_login_at = NOW() WHERE id = $1',
          [user.id]
        )

        // 确保超级管理员用户使用固定ID 99
        const userId = user.username === 'super' ? '99' : String(user.id)
        const token = await generateJwtToken(userId, user.username)

        return c.json({
          message: '登录成功',
          token,
          user: {
            id: userId, // 使用修正后的用户ID
            username: user.username,
            email: user.email
          }
        })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('登录错误:', error)
      return c.json({ error: '登录失败，请稍后重试' }, 500)
    }
  })

  // 获取当前用户信息
  app.get('/api/v1/auth/me', async (c: HonoContext) => {
    const user = await getCurrentUser(c)

    if (!user) {
      return c.json({ error: '未登录' }, 401)
    }

    try {
      const client = await getDbClient()
      try {
        const result = await client.queryObject(
          'SELECT id, username, email, is_active, created_at, last_login_at FROM sexup_users WHERE id = $1',
          [user.id]
        )

        if (result.rows.length === 0) {
          return c.json({ error: '用户不存在' }, 404)
        }

        const userInfo = result.rows[0] as any

        return c.json({
          user: {
            id: String(userInfo.id),
            username: userInfo.username,
            email: userInfo.email,
            isActive: userInfo.is_active,
            createdAt: userInfo.created_at,
            lastLoginAt: userInfo.last_login_at
          }
        })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('获取用户信息错误:', error)
      return c.json({ error: '获取用户信息失败' }, 500)
    }
  })

  // 用户登出（客户端处理，服务端记录）
  app.post('/api/v1/auth/logout', async (c: HonoContext) => {
    const user = await getCurrentUser(c)

    if (!user) {
      return c.json({ error: '未登录' }, 401)
    }

    // 这里可以添加token黑名单逻辑，目前简单返回成功
    return c.json({ message: '登出成功' })
  })

  // 刷新token
  app.post('/api/v1/auth/refresh', async (c: HonoContext) => {
    const user = await getCurrentUser(c)

    if (!user) {
      return c.json({ error: '未登录' }, 401)
    }

    try {
      const newToken = await generateJwtToken(user.id, user.username)
      
      return c.json({
        message: 'Token刷新成功',
        token: newToken
      })
    } catch (error) {
      console.error('Token刷新错误:', error)
      return c.json({ error: 'Token刷新失败' }, 500)
    }
  })

  // 请求重置密码
  app.post('/api/v1/auth/reset-password-request', async (c: HonoContext) => {
    try {
      const { username, email } = await c.req.json()

      if (!username || !email) {
        return c.json({ error: '请输入用户名和邮箱' }, 400)
      }

      const client = await getDbClient()
      try {
        // 验证用户名和邮箱匹配
        const result = await client.queryObject(
          'SELECT id, username, email FROM sexup_users WHERE username = $1 AND email = $2',
          [username, email]
        )

        if (result.rows.length === 0) {
          return c.json({ error: '用户名和邮箱不匹配' }, 400)
        }

        const user = result.rows[0] as any

        // 生成重置令牌（UUID）
        const resetToken = crypto.randomUUID()
        const tokenExpires = new Date(Date.now() + 30 * 60 * 1000) // 30分钟后过期

        // 保存重置令牌到数据库
        await client.queryArray(
          'UPDATE sexup_users SET reset_token = $1, reset_token_expires = $2 WHERE id = $3',
          [resetToken, tokenExpires.toISOString(), user.id]
        )

        // 生成重置链接
        const resetUrl = `${c.req.url.split('/api')[0]}/reset-password?token=${resetToken}`

        // 发送重置密码邮件
        try {
          const emailResponse = await sendResetPasswordEmail(user.email, user.username, resetUrl, tokenExpires)
          
          if (emailResponse.success) {
            return c.json({
              message: '重置密码邮件已发送到您的邮箱，请查收',
              resetUrl // 开发模式下也返回链接作为备用
            })
          } else {
            console.log('邮件发送失败，使用控制台输出作为备用方案')
            console.log('=== 重置密码邮件 ===')
            console.log(`收件人: ${user.email} (${user.username})`)
            console.log(`重置链接: ${resetUrl}`)
            console.log(`有效期: ${tokenExpires.toLocaleString()}`)
            console.log('=====================')
            
            return c.json({
              message: '重置密码邮件发送失败，请查看控制台获取重置链接',
              resetUrl
            })
          }
        } catch (error) {
          console.error('发送邮件时出错:', error)
          // 备用方案：输出到控制台
          console.log('=== 重置密码邮件（备用方案） ===')
          console.log(`收件人: ${user.email} (${user.username})`)
          console.log(`重置链接: ${resetUrl}`)
          console.log(`有效期: ${tokenExpires.toLocaleString()}`)
          console.log('===============================')
          
          return c.json({
            message: '重置密码邮件发送遇到问题，请查看控制台获取重置链接',
            resetUrl
          })
        }
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('重置密码请求错误:', error)
      return c.json({ error: '重置密码请求失败，请稍后重试' }, 500)
    }
  })

  // 重置密码
  app.post('/api/v1/auth/reset-password', async (c: HonoContext) => {
    try {
      const { token, newPassword } = await c.req.json()

      if (!token || !newPassword) {
        return c.json({ error: '请提供重置令牌和新密码' }, 400)
      }

      if (newPassword.length < 6) {
        return c.json({ error: '密码长度至少6个字符' }, 400)
      }

      const client = await getDbClient()
      try {
        // 验证重置令牌
        const result = await client.queryObject(
          'SELECT id, username, email FROM sexup_users WHERE reset_token = $1 AND reset_token_expires > NOW()',
          [token]
        )

        if (result.rows.length === 0) {
          return c.json({ error: '重置令牌无效或已过期' }, 400)
        }

        const user = result.rows[0] as any

        // 哈希新密码
        const hashedPassword = await hashPassword(newPassword)

        // 更新密码并清除重置令牌
        await client.queryArray(
          'UPDATE sexup_users SET password_hash = $1, reset_token = NULL, reset_token_expires = NULL WHERE id = $2',
          [hashedPassword, user.id]
        )

        return c.json({
          message: '密码重置成功，请使用新密码登录'
        })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('密码重置错误:', error)
      return c.json({ error: '密码重置失败，请稍后重试' }, 500)
    }
  })
}
