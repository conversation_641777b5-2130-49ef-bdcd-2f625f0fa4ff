import { Hono } from 'hono'
import type { HonoContext } from '../../../types/index.ts'
import { getDbClient } from '../../../utils/database.ts'

import { requirePermissions } from '../../../utils/rbac.ts'
import { evaluateImage } from '../../../utils/ai-evaluation.ts'

/**
 * RESTful Evaluations API
 * 
 * POST   /api/v1/evaluations     - 创建评估
 * GET    /api/v1/evaluations     - 获取评估列表
 * GET    /api/v1/evaluations/:id - 获取指定评估
 * PUT    /api/v1/evaluations/:id - 更新评估
 * DELETE /api/v1/evaluations/:id - 删除评估
 */

/**
 * 设置评估相关的路由
 *
 * @param app Hono 应用实例
 */
export function setupEvaluationsRoutes(app: Hono) {
  
  // 创建评估
  app.post('/api/v1/evaluations', requirePermissions('evaluations:create'), async (c: HonoContext) => {
    try {
      const currentUser = c.get('user')
      if (!currentUser) {
        return c.json({ error: '未登录' }, 401)
      }

      const { image, mimeType, style, perspective, detail, apiConfig, title, description, isPublic } = await c.req.json()

      if (!image) {
        return c.json({ error: '请上传图片' }, 400)
      }

      // 验证必需的参数并设置默认值
      const evaluationStyle = style || 'civilized'
      const evaluationPerspective = perspective || 'male'
      const evaluationDetail = detail || 'concise'

      if (!evaluationStyle || !evaluationPerspective || !evaluationDetail) {
        return c.json({ error: '评估参数不完整' }, 400)
      }

      // 构建完整的图片 data URL
      const imageUrl = image.startsWith('data:') ? image : `data:${mimeType || 'image/jpeg'};base64,${image}`

      // 记录开始时间
      const startTime = Date.now()

      // 调用AI评估服务
      console.log('开始调用AI评估服务...')
      const evaluationResult = await evaluateImage({
        imageUrl,
        style: evaluationStyle,
        perspective: evaluationPerspective,
        detail: evaluationDetail,
        apiConfig
      })

      // 计算处理时间
      const processingTime = Date.now() - startTime
      console.log(`AI评估完成，耗时: ${processingTime}ms`)

      // 获取配置ID
      const client = await getDbClient()
      try {
        // 查找对应的配置ID
        const [styleResult, perspectiveResult, detailResult] = await Promise.all([
          client.queryObject('SELECT id FROM sexup_evaluation_styles WHERE code = $1', [evaluationStyle]),
          client.queryObject('SELECT id FROM sexup_evaluation_perspectives WHERE code = $1', [evaluationPerspective]),
          client.queryObject('SELECT id FROM sexup_evaluation_details WHERE code = $1', [evaluationDetail])
        ])

        const styleId = (styleResult.rows[0] as any)?.id || null
        const perspectiveId = (perspectiveResult.rows[0] as any)?.id || null
        const detailId = (detailResult.rows[0] as any)?.id || null

        // 保存评估记录到数据库
        const result = await client.queryObject(`
          INSERT INTO sexup_evaluations (
            user_id, title, description, image_data, mime_type,
            style, perspective, detail,
            style_id, perspective_id, detail_id, style_code, perspective_code, detail_code,
            verdict, rating, explanation, api_config, processing_time_ms, is_public, created_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, NOW())
          RETURNING id, created_at
        `, [
          Number(currentUser.id),
          title || '图片评估',
          description || '',
          image,
          mimeType,
          evaluationStyle,      // style 列
          evaluationPerspective, // perspective 列
          evaluationDetail,     // detail 列
          styleId,
          perspectiveId,
          detailId,
          evaluationStyle,      // style_code 列
          evaluationPerspective, // perspective_code 列
          evaluationDetail,     // detail_code 列
          evaluationResult.verdict,
          evaluationResult.rating,
          evaluationResult.explanation,
          JSON.stringify(apiConfig || {}),
          processingTime, // 实际处理时间
          isPublic || false // is_public 字段
        ])

        const evaluation = result.rows[0] as any

        return c.json({
          id: String(evaluation.id),
          ...evaluationResult,
          createdAt: evaluation.created_at
        }, 201)
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('创建评估失败:', error)
      return c.json({ error: '创建评估失败' }, 500)
    }
  })

  // 保存已有的评估结果（不进行AI评估）
  app.post('/api/v1/evaluations/save', requirePermissions('evaluations:create'), async (c: HonoContext) => {
    try {
      const currentUser = c.get('user')
      if (!currentUser) {
        return c.json({ error: '未登录' }, 401)
      }

      const {
        image, mimeType, style, perspective, detail,
        title, description, verdict, rating, explanation, isPublic
      } = await c.req.json()

      if (!image || !verdict || rating === undefined || !explanation) {
        return c.json({ error: '评估结果数据不完整' }, 400)
      }

      // 检查image是否为URL（CDN地址）还是base64数据
      const isImageUrl = image.startsWith('http://') || image.startsWith('https://')

      console.log('保存评估结果:', {
        imageType: isImageUrl ? 'CDN URL' : 'Base64 Data',
        imageLength: image.length,
        verdict,
        rating
      })

      // 验证必需的参数并设置默认值
      const evaluationStyle = style || 'civilized'
      const evaluationPerspective = perspective || 'male'
      const evaluationDetail = detail || 'concise'

      // 获取配置ID
      const client = await getDbClient()
      try {
        // 查找对应的配置ID
        const [styleResult, perspectiveResult, detailResult] = await Promise.all([
          client.queryObject('SELECT id FROM sexup_evaluation_styles WHERE code = $1', [evaluationStyle]),
          client.queryObject('SELECT id FROM sexup_evaluation_perspectives WHERE code = $1', [evaluationPerspective]),
          client.queryObject('SELECT id FROM sexup_evaluation_details WHERE code = $1', [evaluationDetail])
        ])

        const styleId = (styleResult.rows[0] as any)?.id || null
        const perspectiveId = (perspectiveResult.rows[0] as any)?.id || null
        const detailId = (detailResult.rows[0] as any)?.id || null

        // 直接保存评估记录到数据库（不进行AI评估）
        const result = await client.queryObject(`
          INSERT INTO sexup_evaluations (
            user_id, title, description, image_data, mime_type,
            style, perspective, detail,
            style_id, perspective_id, detail_id, style_code, perspective_code, detail_code,
            verdict, rating, explanation, api_config, processing_time_ms, is_public, created_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, NOW())
          RETURNING id, created_at
        `, [
          Number(currentUser.id),
          title || '图片评估',
          description || '',
          image,
          mimeType,
          evaluationStyle,      // style 列
          evaluationPerspective, // perspective 列
          evaluationDetail,     // detail 列
          styleId,
          perspectiveId,
          detailId,
          evaluationStyle,      // style_code 列
          evaluationPerspective, // perspective_code 列
          evaluationDetail,     // detail_code 列
          verdict,
          rating,
          explanation,
          JSON.stringify({}),   // 空的API配置
          0, // 处理时间设为0，因为没有AI评估
          isPublic || false     // is_public 字段
        ])

        const evaluation = result.rows[0] as any

        return c.json({
          id: String(evaluation.id),
          verdict,
          rating,
          explanation,
          createdAt: evaluation.created_at,
          message: '评估结果已保存'
        }, 201)
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('保存评估结果失败:', error)
      return c.json({ error: '保存评估结果失败' }, 500)
    }
  })

  // 批量删除评估
  app.delete('/api/v1/evaluations/batch', requirePermissions('evaluations:delete'), async (c: HonoContext) => {
    try {
      const currentUser = c.get('user')
      if (!currentUser) {
        return c.json({ error: '未登录' }, 401)
      }

      const { ids } = await c.req.json()

      if (!Array.isArray(ids) || ids.length === 0) {
        return c.json({ error: '请提供要删除的评估ID数组' }, 400)
      }

      if (ids.length > 100) {
        return c.json({ error: '一次最多删除100个评估' }, 400)
      }

      console.log(`用户 ${currentUser.id} 请求批量删除评估:`, ids)

      const client = await getDbClient()
      try {
        // 构建IN查询的占位符
        const placeholders = ids.map((_, index) => `$${index + 2}`).join(',')

        // 先检查这些评估是否都属于当前用户
        const checkResult = await client.queryObject(
          `SELECT id FROM sexup_evaluations WHERE id IN (${placeholders}) AND user_id = $1`,
          [Number(currentUser.id), ...ids.map(id => String(id))]
        )

        const existingIds = checkResult.rows.map((row: any) => String(row.id))
        const notFoundIds = ids.filter(id => !existingIds.includes(String(id)))

        if (notFoundIds.length > 0) {
          console.log(`评估不存在或不属于用户: ${notFoundIds.join(', ')}`)
        }

        // 删除存在的评估
        let deletedCount = 0
        if (existingIds.length > 0) {
          const deletePlaceholders = existingIds.map((_, index) => `$${index + 2}`).join(',')
          const deleteResult = await client.queryObject(
            `DELETE FROM sexup_evaluations WHERE id IN (${deletePlaceholders}) AND user_id = $1`,
            [Number(currentUser.id), ...existingIds]
          )
          deletedCount = deleteResult.rowCount || 0
        }

        return c.json({
          success: true,
          deletedCount,
          requestedCount: ids.length,
          deletedIds: existingIds,
          notFoundIds,
          message: `成功删除 ${deletedCount} 个评估${notFoundIds.length > 0 ? `，${notFoundIds.length} 个评估不存在或无权限` : ''}`
        }, 200)

      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('批量删除评估失败:', error)
      return c.json({ error: '批量删除评估失败' }, 500)
    }
  })

  // 获取评估列表
  app.get('/api/v1/evaluations', requirePermissions('evaluations:list'), async (c: HonoContext) => {
    try {
      const currentUser = c.get('user')
      if (!currentUser) {
        return c.json({ error: '未登录' }, 401)
      }

      const page = parseInt(c.req.query('page') || '1')
      const limit = parseInt(c.req.query('limit') || '20')
      const userId = c.req.query('userId') // 可选：筛选特定用户的评估
      const offset = (page - 1) * limit

      const client = await getDbClient()
      try {
        let query = `
          SELECT e.id, e.title, e.description, e.image_data, e.mime_type,
                 e.style, e.perspective, e.detail, e.style_code, e.perspective_code, e.detail_code,
                 e.verdict, e.rating, e.explanation, e.is_public, e.created_at, e.updated_at,
                 u.username as creator_username
          FROM sexup_evaluations e
          INNER JOIN sexup_users u ON e.user_id = u.id
        `
        const params: any[] = []
        const conditions: string[] = []

        // 如果不是管理员，只能看到自己的评估
        if (userId) {
          conditions.push(`e.user_id = $${params.length + 1}`)
          params.push(parseInt(userId))
        } else {
          // 普通用户只能看到自己的评估
          conditions.push(`e.user_id = $${params.length + 1}`)
          params.push(Number(currentUser.id))
        }

        if (conditions.length > 0) {
          query += ` WHERE ${conditions.join(' AND ')}`
        }

        query += ` ORDER BY e.created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`
        params.push(limit, offset)

        const result = await client.queryObject(query, params)

        // 获取总数
        let countQuery = 'SELECT COUNT(*) as total FROM sexup_evaluations e'
        const countParams: any[] = []

        if (conditions.length > 0) {
          countQuery += ` WHERE ${conditions.join(' AND ')}`
          countParams.push(...params.slice(0, -2)) // 排除 limit 和 offset
        }

        const countResult = await client.queryObject(countQuery, countParams)
        const total = Number((countResult.rows[0] as any).total)

        // 转换BigInt为字符串
        const evaluations = result.rows.map(row => ({
          ...(row as any),
          id: String((row as any).id),
          user_id: String((row as any).user_id),
          rating: Number((row as any).rating)
        }))

        return c.json({
          evaluations,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit)
          }
        })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('获取评估列表失败:', error)
      return c.json({ error: '获取评估列表失败' }, 500)
    }
  })

  // 获取指定评估
  app.get('/api/v1/evaluations/:id', requirePermissions('evaluations:read'), async (c: HonoContext) => {
    try {
      const evaluationId = parseInt(c.req.param('id'))
      const currentUser = c.get('user')
      if (!currentUser) {
        return c.json({ error: '未登录' }, 401)
      }

      const client = await getDbClient()
      try {
        const result = await client.queryObject(`
          SELECT e.*, u.username as creator_username
          FROM sexup_evaluations e
          INNER JOIN sexup_users u ON e.user_id = u.id
          WHERE e.id = $1
        `, [evaluationId])

        if (result.rows.length === 0) {
          return c.json({ error: '评估不存在' }, 404)
        }

        const evaluation = result.rows[0] as any

        // 检查访问权限：创建者、管理员或公开评估可以查看
        if (Number(evaluation.user_id) !== Number(currentUser.id) && !evaluation.is_public) {
          // 这里应该检查是否有管理员权限
          return c.json({ error: '无权访问此评估' }, 403)
        }

        // 转换BigInt为字符串
        const formattedEvaluation = {
          ...evaluation,
          id: String(evaluation.id),
          user_id: String(evaluation.user_id),
          rating: Number(evaluation.rating),
          style_id: evaluation.style_id ? String(evaluation.style_id) : null,
          perspective_id: evaluation.perspective_id ? String(evaluation.perspective_id) : null,
          detail_id: evaluation.detail_id ? String(evaluation.detail_id) : null,
          api_config_id: evaluation.api_config_id ? String(evaluation.api_config_id) : null,
          processing_time_ms: evaluation.processing_time_ms ? Number(evaluation.processing_time_ms) : null
        }

        return c.json({ evaluation: formattedEvaluation })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('获取评估失败:', error)
      return c.json({ error: '获取评估失败' }, 500)
    }
  })

  // 更新评估
  app.put('/api/v1/evaluations/:id', requirePermissions('evaluations:update'), async (c: HonoContext) => {
    try {
      const evaluationId = parseInt(c.req.param('id'))
      const currentUser = c.get('user')
      if (!currentUser) {
        return c.json({ error: '未登录' }, 401)
      }

      const { title, description } = await c.req.json()

      const client = await getDbClient()
      try {
        // 检查评估是否存在且属于当前用户
        const checkResult = await client.queryObject(
          'SELECT user_id FROM sexup_evaluations WHERE id = $1',
          [evaluationId]
        )

        if (checkResult.rows.length === 0) {
          return c.json({ error: '评估不存在' }, 404)
        }

        const evaluation = checkResult.rows[0] as any
        if (Number(evaluation.user_id) !== Number(currentUser.id)) {
          return c.json({ error: '无权修改此评估' }, 403)
        }

        // 更新评估
        const updates: string[] = []
        const params: any[] = []
        let paramIndex = 1

        if (title !== undefined) {
          updates.push(`title = $${paramIndex++}`)
          params.push(title)
        }

        if (description !== undefined) {
          updates.push(`description = $${paramIndex++}`)
          params.push(description)
        }

        if (updates.length === 0) {
          return c.json({ error: '没有要更新的字段' }, 400)
        }

        updates.push(`updated_at = NOW()`)
        params.push(evaluationId)

        const query = `
          UPDATE sexup_evaluations 
          SET ${updates.join(', ')}
          WHERE id = $${paramIndex}
          RETURNING id, title, description, updated_at
        `

        const result = await client.queryObject(query, params)

        // 转换BigInt为字符串
        const updatedEvaluation = {
          ...(result.rows[0] as any),
          id: String((result.rows[0] as any).id)
        }

        return c.json({
          message: '评估更新成功',
          evaluation: updatedEvaluation
        })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('更新评估失败:', error)
      return c.json({ error: '更新评估失败' }, 500)
    }
  })

  // 删除评估
  app.delete('/api/v1/evaluations/:id', requirePermissions('evaluations:delete'), async (c: HonoContext) => {
    try {
      const evaluationId = parseInt(c.req.param('id'))
      const currentUser = c.get('user')
      if (!currentUser) {
        return c.json({ error: '未登录' }, 401)
      }

      const client = await getDbClient()
      try {
        // 检查评估是否存在且属于当前用户
        const checkResult = await client.queryObject(
          'SELECT user_id FROM sexup_evaluations WHERE id = $1',
          [evaluationId]
        )

        if (checkResult.rows.length === 0) {
          return c.json({ error: '评估不存在' }, 404)
        }

        const evaluation = checkResult.rows[0] as any
        if (Number(evaluation.user_id) !== Number(currentUser.id)) {
          return c.json({ error: '无权删除此评估' }, 403)
        }

        // 删除评估
        await client.queryArray(
          'DELETE FROM sexup_evaluations WHERE id = $1',
          [evaluationId]
        )

        return c.json({ message: '评估删除成功' })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('删除评估失败:', error)
      return c.json({ error: '删除评估失败' }, 500)
    }
  })

  // 获取公开评估列表（无需认证）
  app.get('/api/v1/evaluations/public', async (c: HonoContext) => {
    try {
      const page = parseInt(c.req.query('page') || '1')
      const limit = Math.min(parseInt(c.req.query('limit') || '10'), 10) // 最多10条
      const offset = (page - 1) * limit

      const client = await getDbClient()
      try {
        const result = await client.queryObject(`
          SELECT e.id, e.title, e.description, e.image_data, e.mime_type,
                 e.style, e.perspective, e.detail, e.style_code, e.perspective_code, e.detail_code,
                 e.verdict, e.rating, e.explanation, e.created_at,
                 u.username as creator_username
          FROM sexup_evaluations e
          INNER JOIN sexup_users u ON e.user_id = u.id
          WHERE e.is_public = true
          ORDER BY e.created_at DESC
          LIMIT $1 OFFSET $2
        `, [limit, offset])

        // 获取总数
        const countResult = await client.queryObject(
          'SELECT COUNT(*) as total FROM sexup_evaluations WHERE is_public = true'
        )
        const total = Number((countResult.rows[0] as any).total)

        // 转换BigInt为字符串
        const evaluations = result.rows.map(row => ({
          ...(row as any),
          id: String((row as any).id),
          user_id: String((row as any).user_id),
          rating: Number((row as any).rating)
        }))

        return c.json({
          evaluations,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit),
            hasMore: page < Math.ceil(total / limit)
          }
        })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('获取公开评估列表失败:', error)
      return c.json({ error: '获取公开评估列表失败' }, 500)
    }
  })

  // 获取公开评估详情（无需认证）
  app.get('/api/v1/evaluations/public/:id', async (c: HonoContext) => {
    try {
      const evaluationId = parseInt(c.req.param('id'))

      const client = await getDbClient()
      try {
        const result = await client.queryObject(`
          SELECT e.*, u.username as creator_username
          FROM sexup_evaluations e
          INNER JOIN sexup_users u ON e.user_id = u.id
          WHERE e.id = $1 AND e.is_public = true
        `, [evaluationId])

        if (result.rows.length === 0) {
          return c.json({ error: '公开评估不存在' }, 404)
        }

        const evaluation = result.rows[0] as any

        // 转换BigInt为字符串
        const formattedEvaluation = {
          ...evaluation,
          id: String(evaluation.id),
          user_id: String(evaluation.user_id),
          rating: Number(evaluation.rating),
          style_id: evaluation.style_id ? String(evaluation.style_id) : null,
          perspective_id: evaluation.perspective_id ? String(evaluation.perspective_id) : null,
          detail_id: evaluation.detail_id ? String(evaluation.detail_id) : null,
          api_config_id: evaluation.api_config_id ? String(evaluation.api_config_id) : null,
          processing_time_ms: evaluation.processing_time_ms ? Number(evaluation.processing_time_ms) : null
        }

        return c.json({ evaluation: formattedEvaluation })
      } finally {
        await client.end()
      }
    } catch (error) {
      console.error('获取公开评估详情失败:', error)
      return c.json({ error: '获取公开评估详情失败' }, 500)
    }
  })
}
