import { Hono } from 'hono'
import type { HonoContext } from '../../types/index.ts'
import { setupV1ApiRoutes } from './v1/index.ts'

export function setupApiRoutes(app: Hono) {
  // RESTful API v1 (统一API版本)
  setupV1ApiRoutes(app)

  // 重定向旧的API状态检查到v1
  app.get('/api/status', (c: HonoContext) => {
    return c.redirect('/api/v1/status', 301)
  })

  // 404处理 - 所有其他/api/*请求
  app.all('/api/*', (c: HonoContext) => {
    return c.json({
      error: 'API端点不存在',
      message: '请使用v1 API端点',
      path: c.req.path,
      method: c.req.method,
      documentation: '/api/v1/docs'
    }, 404)
  })
}
