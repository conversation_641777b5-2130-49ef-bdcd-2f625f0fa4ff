import { getDbClient } from './database.ts'

/**
 * UUID主键迁移工具
 * 为新表使用UUID主键，现有表保持SERIAL
 */

// 启用UUID扩展
export async function enableUuidExtension() {
  const client = await getDbClient()
  try {
    await client.queryArray('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')
    console.log('UUID扩展已启用')
  } catch (error) {
    console.error('启用UUID扩展失败:', error)
    throw error
  } finally {
    await client.end()
  }
}

// 检查表的回填状态
async function checkBackfillStatus(client: any, tableName: string): Promise<boolean> {
  try {
    const result = await client.queryObject(`
      SELECT job_type, status, description
      FROM [SHOW JOBS]
      WHERE job_type = 'SCHEMA CHANGE'
      AND description LIKE '%${tableName}%'
      AND status IN ('running', 'pending')
    `)
    return result.rows.length === 0 // 没有正在运行的schema change job
  } catch (error) {
    console.warn(`检查表 ${tableName} 回填状态失败:`, error)
    return true // 假设可以继续
  }
}

// 等待回填完成
async function waitForBackfillCompletion(client: any, tableName: string, maxWaitSeconds = 60) {
  console.log(`等待表 ${tableName} 回填完成...`)
  const startTime = Date.now()

  while (Date.now() - startTime < maxWaitSeconds * 1000) {
    const isReady = await checkBackfillStatus(client, tableName)
    if (isReady) {
      console.log(`表 ${tableName} 回填完成`)
      return true
    }

    console.log(`表 ${tableName} 仍在回填中，等待5秒...`)
    await new Promise(resolve => setTimeout(resolve, 5000))
  }

  console.warn(`表 ${tableName} 回填超时，跳过后续操作`)
  return false
}

// 为现有表添加UUID字段（可选，为未来迁移做准备）
export async function addUuidColumns() {
  const client = await getDbClient()
  try {
    const tables = [
      'sexup_users',
      'sexup_roles',
      'sexup_permissions',
      'sexup_evaluations',
      'sexup_user_roles',
      'sexup_role_permissions'
    ]

    for (const table of tables) {
      try {
        console.log(`开始处理表 ${table}...`)

        // 检查列是否已存在
        const columnExists = await client.queryObject(`
          SELECT column_name
          FROM information_schema.columns
          WHERE table_name = $1 AND column_name = 'uuid'
        `, [table])

        if (columnExists.rows.length > 0) {
          console.log(`表 ${table} 的UUID字段已存在，跳过`)
          continue
        }

        // 添加UUID字段（不设置默认值，避免大表回填问题）
        await client.queryArray(`
          ALTER TABLE ${table}
          ADD COLUMN uuid UUID
        `)

        // 等待回填完成
        const backfillReady = await waitForBackfillCompletion(client, table)
        if (!backfillReady) {
          console.warn(`表 ${table} 回填未完成，跳过后续操作`)
          continue
        }

        // 分批为现有记录生成UUID（避免大事务）
        const batchSize = 1000
        let updatedRows = 0

        while (true) {
          const result = await client.queryArray(`
            UPDATE ${table}
            SET uuid = uuid_generate_v4()
            WHERE uuid IS NULL
            AND id IN (
              SELECT id FROM ${table}
              WHERE uuid IS NULL
              LIMIT ${batchSize}
            )
          `)

          updatedRows += result.rowCount || 0

          if ((result.rowCount || 0) < batchSize) {
            break // 没有更多记录需要更新
          }

          console.log(`表 ${table} 已更新 ${updatedRows} 条记录...`)

          // 短暂休息，避免对数据库造成压力
          await new Promise(resolve => setTimeout(resolve, 100))
        }

        console.log(`表 ${table} 总共更新了 ${updatedRows} 条记录`)

        // 创建唯一索引
        await client.queryArray(`
          CREATE UNIQUE INDEX IF NOT EXISTS idx_${table}_uuid
          ON ${table}(uuid)
        `)

        console.log(`表 ${table} UUID字段添加完成`)

      } catch (error) {
        if (error instanceof Error && error.message?.includes('being backfilled')) {
          console.warn(`表 ${table} 正在回填中，跳过此表`)
        } else {
          console.error(`处理表 ${table} 失败:`, error)
        }
      }
    }
  } finally {
    await client.end()
  }
}

// 创建使用UUID主键的新表示例
export async function createUuidTable(tableName: string, columns: string) {
  const client = await getDbClient()
  try {
    await client.queryArray(`
      CREATE TABLE IF NOT EXISTS ${tableName} (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        ${columns},
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `)
    console.log(`UUID表 ${tableName} 创建完成`)
  } finally {
    await client.end()
  }
}

// 新表模板：使用UUID主键的日志表
export async function createLogTable() {
  await createUuidTable('sexup_audit_logs', `
    user_id INTEGER REFERENCES sexup_users(id),
    action VARCHAR(50) NOT NULL,
    resource VARCHAR(50) NOT NULL,
    resource_id VARCHAR(50),
    details JSONB,
    ip_address INET,
    user_agent TEXT
  `)
}

// 新表模板：使用UUID主键的通知表  
export async function createNotificationTable() {
  await createUuidTable('sexup_notifications', `
    user_id INTEGER REFERENCES sexup_users(id),
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(20) DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    expires_at TIMESTAMP
  `)
}

// 新表模板：使用UUID主键的文件表
export async function createFileTable() {
  await createUuidTable('sexup_files', `
    user_id INTEGER REFERENCES sexup_users(id),
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_size BIGINT NOT NULL,
    file_path TEXT NOT NULL,
    is_public BOOLEAN DEFAULT FALSE
  `)
}

// 执行新表UUID迁移
export async function migrateToUuidTables() {
  try {
    console.log('开始UUID表迁移...')
    
    await enableUuidExtension()
    await addUuidColumns() // 可选：为现有表添加UUID字段
    
    // 创建新的UUID表
    await createLogTable()
    await createNotificationTable() 
    await createFileTable()
    
    console.log('UUID表迁移完成')
  } catch (error) {
    console.error('UUID表迁移失败:', error)
    throw error
  }
}
