import { getDbClient } from './database.ts'

/**
 * UUID主键迁移工具
 * 为新表使用UUID主键，现有表保持SERIAL
 */

// 启用UUID扩展
export async function enableUuidExtension() {
  const client = await getDbClient()
  try {
    await client.queryArray('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')
    console.log('UUID扩展已启用')
  } catch (error) {
    console.error('启用UUID扩展失败:', error)
    throw error
  } finally {
    await client.end()
  }
}

// 为现有表添加UUID字段（可选，为未来迁移做准备）
export async function addUuidColumns() {
  const client = await getDbClient()
  try {
    const tables = [
      'sexup_users',
      'sexup_roles', 
      'sexup_permissions',
      'sexup_evaluations',
      'sexup_user_roles',
      'sexup_role_permissions'
    ]

    for (const table of tables) {
      try {
        // 添加UUID字段
        await client.queryArray(`
          ALTER TABLE ${table} 
          ADD COLUMN IF NOT EXISTS uuid UUID DEFAULT uuid_generate_v4()
        `)
        
        // 为现有记录生成UUID
        await client.queryArray(`
          UPDATE ${table} 
          SET uuid = uuid_generate_v4() 
          WHERE uuid IS NULL
        `)
        
        // 创建唯一索引
        await client.queryArray(`
          CREATE UNIQUE INDEX IF NOT EXISTS idx_${table}_uuid 
          ON ${table}(uuid)
        `)
        
        console.log(`表 ${table} UUID字段添加完成`)
      } catch (error) {
        console.error(`处理表 ${table} 失败:`, error)
      }
    }
  } finally {
    await client.end()
  }
}

// 创建使用UUID主键的新表示例
export async function createUuidTable(tableName: string, columns: string) {
  const client = await getDbClient()
  try {
    await client.queryArray(`
      CREATE TABLE IF NOT EXISTS ${tableName} (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        ${columns},
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `)
    console.log(`UUID表 ${tableName} 创建完成`)
  } finally {
    await client.end()
  }
}

// 新表模板：使用UUID主键的日志表
export async function createLogTable() {
  await createUuidTable('sexup_audit_logs', `
    user_id INTEGER REFERENCES sexup_users(id),
    action VARCHAR(50) NOT NULL,
    resource VARCHAR(50) NOT NULL,
    resource_id VARCHAR(50),
    details JSONB,
    ip_address INET,
    user_agent TEXT
  `)
}

// 新表模板：使用UUID主键的通知表  
export async function createNotificationTable() {
  await createUuidTable('sexup_notifications', `
    user_id INTEGER REFERENCES sexup_users(id),
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(20) DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    expires_at TIMESTAMP
  `)
}

// 新表模板：使用UUID主键的文件表
export async function createFileTable() {
  await createUuidTable('sexup_files', `
    user_id INTEGER REFERENCES sexup_users(id),
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_size BIGINT NOT NULL,
    file_path TEXT NOT NULL,
    is_public BOOLEAN DEFAULT FALSE
  `)
}

// 执行新表UUID迁移
export async function migrateToUuidTables() {
  try {
    console.log('开始UUID表迁移...')
    
    await enableUuidExtension()
    await addUuidColumns() // 可选：为现有表添加UUID字段
    
    // 创建新的UUID表
    await createLogTable()
    await createNotificationTable() 
    await createFileTable()
    
    console.log('UUID表迁移完成')
  } catch (error) {
    console.error('UUID表迁移失败:', error)
    throw error
  }
}
