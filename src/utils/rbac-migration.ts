import { getDbClient } from './database.ts'

/**
 * RBAC权限系统数据库迁移
 * 
 * 权限系统设计：
 * - 用户(users) -> 角色(roles) -> 权限(permissions)
 * - 支持多角色分配
 * - 细粒度权限控制
 */

// 预定义的系统角色
export const SYSTEM_ROLES = {
  SUPER_ADMIN: 'super_admin',     // 超级管理员
  ADMIN: 'admin',                 // 管理员
  MODERATOR: 'moderator',         // 版主
  VIP_USER: 'vip_user',          // VIP用户
  REGULAR_USER: 'regular_user',   // 普通用户
  GUEST: 'guest'                  // 访客
} as const

// 预定义的系统权限
export const SYSTEM_PERMISSIONS = {
  // 用户管理权限
  'users:create': '创建用户',
  'users:read': '查看用户',
  'users:update': '更新用户',
  'users:delete': '删除用户',
  'users:list': '用户列表',
  
  // 评估相关权限
  'evaluations:create': '创建评估',
  'evaluations:read': '查看评估',
  'evaluations:update': '更新评估',
  'evaluations:delete': '删除评估',
  'evaluations:list': '评估列表',
  
  // 分享相关权限
  'shares:create': '创建分享',
  'shares:read': '查看分享',
  'shares:update': '更新分享',
  'shares:delete': '删除分享',
  'shares:list': '分享列表',
  'shares:public': '公开分享',
  
  // 角色管理权限
  'roles:create': '创建角色',
  'roles:read': '查看角色',
  'roles:update': '更新角色',
  'roles:delete': '删除角色',
  'roles:assign': '分配角色',
  
  // 系统管理权限
  'system:admin': '系统管理',
  'system:config': '系统配置',
  'system:logs': '查看日志',
  'system:stats': '查看统计'
} as const

// 角色权限映射
export const ROLE_PERMISSIONS = {
  [SYSTEM_ROLES.SUPER_ADMIN]: Object.keys(SYSTEM_PERMISSIONS),
  [SYSTEM_ROLES.ADMIN]: [
    'users:read', 'users:list', 'users:update',
    'evaluations:read', 'evaluations:list', 'evaluations:delete',
    'shares:read', 'shares:list', 'shares:delete', 'shares:public',
    'roles:read', 'roles:assign',
    'system:stats'
  ],
  [SYSTEM_ROLES.MODERATOR]: [
    'users:read', 'users:list',
    'evaluations:read', 'evaluations:list',
    'shares:read', 'shares:list', 'shares:delete'
  ],
  [SYSTEM_ROLES.VIP_USER]: [
    'users:read', 'users:update',
    'evaluations:create', 'evaluations:read', 'evaluations:update', 'evaluations:delete',
    'shares:create', 'shares:read', 'shares:update', 'shares:delete', 'shares:public'
  ],
  [SYSTEM_ROLES.REGULAR_USER]: [
    'users:read', 'users:update',
    'evaluations:create', 'evaluations:read', 'evaluations:update', 'evaluations:delete',
    'shares:create', 'shares:read', 'shares:update', 'shares:delete'
  ],
  [SYSTEM_ROLES.GUEST]: [
    'evaluations:read',
    'shares:read'
  ]
}

/**
 * 执行RBAC权限系统数据库迁移
 */
export async function migrateRbacTables() {
  const client = await getDbClient()

  try {
    console.log('开始RBAC权限系统数据库迁移...')

    // 不需要再次删除表，因为database.ts已经处理了
    // 如果需要单独运行此迁移，可以取消下面的注释
    /*
    await client.queryArray(`DROP TABLE IF EXISTS sexup_user_roles CASCADE`)
    await client.queryArray(`DROP TABLE IF EXISTS sexup_role_permissions CASCADE`)
    await client.queryArray(`DROP TABLE IF EXISTS sexup_permissions CASCADE`)
    await client.queryArray(`DROP TABLE IF EXISTS sexup_roles CASCADE`)
    await client.queryArray(`DROP TABLE IF EXISTS sexup_evaluations CASCADE`)
    */

    // 1. 创建角色表
    await client.queryArray(`
      CREATE TABLE IF NOT EXISTS sexup_roles (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(50) UNIQUE NOT NULL,
        display_name VARCHAR(100) NOT NULL,
        description TEXT,
        is_system BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `)
    
    // 2. 创建权限表
    await client.queryArray(`
      CREATE TABLE IF NOT EXISTS sexup_permissions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(100) UNIQUE NOT NULL,
        display_name VARCHAR(100) NOT NULL,
        description TEXT,
        resource VARCHAR(50) NOT NULL,
        action VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT NOW()
      )
    `)
    
    // 3. 创建角色权限关联表
    await client.queryArray(`
      CREATE TABLE IF NOT EXISTS sexup_role_permissions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        role_id UUID REFERENCES sexup_roles(id) ON DELETE CASCADE,
        permission_id UUID REFERENCES sexup_permissions(id) ON DELETE CASCADE,
        created_at TIMESTAMP DEFAULT NOW(),
        UNIQUE(role_id, permission_id)
      )
    `)
    
    // 4. 创建用户角色关联表
    await client.queryArray(`
      CREATE TABLE IF NOT EXISTS sexup_user_roles (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES sexup_users(id) ON DELETE CASCADE,
        role_id UUID REFERENCES sexup_roles(id) ON DELETE CASCADE,
        assigned_by UUID REFERENCES sexup_users(id),
        assigned_at TIMESTAMP DEFAULT NOW(),
        expires_at TIMESTAMP,
        UNIQUE(user_id, role_id)
      )
    `)
    
    // 5. 扩展用户表添加默认角色字段（如果不存在）
    try {
      await client.queryArray(`
        ALTER TABLE sexup_users
        ADD COLUMN IF NOT EXISTS default_role_id UUID REFERENCES sexup_roles(id)
      `)
    } catch (error) {
      console.log('default_role_id字段可能已存在:', error instanceof Error ? error.message : '未知错误')
    }
    
    // 6. 创建评估配置表
    await client.queryArray(`
      CREATE TABLE IF NOT EXISTS sexup_evaluation_styles (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        code VARCHAR(20) UNIQUE NOT NULL,
        name VARCHAR(50) NOT NULL,
        description TEXT,
        prompt_template TEXT NOT NULL,
        icon VARCHAR(10),
        color VARCHAR(20),
        is_active BOOLEAN DEFAULT TRUE,
        sort_order INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT NOW()
      )
    `)

    await client.queryArray(`
      CREATE TABLE IF NOT EXISTS sexup_evaluation_perspectives (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        code VARCHAR(20) UNIQUE NOT NULL,
        name VARCHAR(50) NOT NULL,
        description TEXT,
        prompt_template TEXT NOT NULL,
        icon VARCHAR(10),
        color VARCHAR(20),
        is_active BOOLEAN DEFAULT TRUE,
        sort_order INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT NOW()
      )
    `)

    await client.queryArray(`
      CREATE TABLE IF NOT EXISTS sexup_evaluation_details (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        code VARCHAR(20) UNIQUE NOT NULL,
        name VARCHAR(50) NOT NULL,
        description TEXT,
        prompt_template TEXT NOT NULL,
        icon VARCHAR(10),
        is_active BOOLEAN DEFAULT TRUE,
        sort_order INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT NOW()
      )
    `)

    await client.queryArray(`
      CREATE TABLE IF NOT EXISTS sexup_api_configs (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES sexup_users(id) ON DELETE CASCADE,
        name VARCHAR(100) NOT NULL,
        base_url VARCHAR(500) NOT NULL,
        api_key VARCHAR(500) NOT NULL,
        model VARCHAR(100) NOT NULL,
        is_default BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `)

    // 7. 创建评估表
    await client.queryArray(`
      CREATE TABLE IF NOT EXISTS sexup_evaluations (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES sexup_users(id) ON DELETE CASCADE,
        title VARCHAR(200) NOT NULL DEFAULT '图片评估',
        description TEXT,
        image_data TEXT NOT NULL,
        mime_type VARCHAR(50),
        style VARCHAR(20) NOT NULL,
        perspective VARCHAR(20) NOT NULL,
        detail VARCHAR(20) NOT NULL,
        style_code VARCHAR(20),
        perspective_code VARCHAR(20),
        detail_code VARCHAR(20),
        verdict VARCHAR(10) NOT NULL,
        rating INTEGER NOT NULL,
        explanation TEXT NOT NULL,
        api_config JSONB,
        style_id UUID REFERENCES sexup_evaluation_styles(id),
        perspective_id UUID REFERENCES sexup_evaluation_perspectives(id),
        detail_id UUID REFERENCES sexup_evaluation_details(id),
        api_config_id UUID REFERENCES sexup_api_configs(id),
        processing_time_ms INTEGER,
        is_public BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `)

    // 为现有的评估表添加is_public字段（如果不存在）
    try {
      await client.queryArray(`
        ALTER TABLE sexup_evaluations
        ADD COLUMN IF NOT EXISTS is_public BOOLEAN DEFAULT FALSE
      `)
    } catch (error) {
      console.log('is_public字段可能已存在:', error instanceof Error ? error.message : '未知错误')
    }

    // 10. 创建索引优化查询性能
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_role_permissions_role_id ON sexup_role_permissions(role_id)
    `)
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_role_permissions_permission_id ON sexup_role_permissions(permission_id)
    `)
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON sexup_user_roles(user_id)
    `)
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_user_roles_role_id ON sexup_user_roles(role_id)
    `)
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_users_default_role ON sexup_users(default_role_id)
    `)
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_evaluations_user_id ON sexup_evaluations(user_id)
    `)
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_evaluations_created_at ON sexup_evaluations(created_at)
    `)
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_evaluations_style_id ON sexup_evaluations(style_id)
    `)
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_evaluations_perspective_id ON sexup_evaluations(perspective_id)
    `)
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_evaluations_detail_id ON sexup_evaluations(detail_id)
    `)
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_evaluations_public ON sexup_evaluations(is_public)
    `)
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_api_configs_user_id ON sexup_api_configs(user_id)
    `)
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_api_configs_is_default ON sexup_api_configs(is_default)
    `)
    
    console.log('RBAC权限表创建完成')

    // 等待一下确保表完全创建
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 11. 初始化系统角色和权限
    await initializeSystemRolesAndPermissions(client)

    // 12. 初始化评估配置数据
    await initializeEvaluationConfigs(client)

    console.log('RBAC权限系统数据库迁移完成')
    
  } catch (error) {
    console.error('RBAC权限系统迁移失败:', error)
    throw error
  } finally {
    await client.end()
  }
}

/**
 * 初始化系统角色和权限
 */
async function initializeSystemRolesAndPermissions(client: any) {
  console.log('初始化系统角色和权限...')
  
  try {
    // 先检查必要的表是否存在
    const tables = ['sexup_permissions', 'sexup_roles', 'sexup_role_permissions']
    for (const table of tables) {
      const result = await client.queryArray(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = $1
        )
      `, [table])
      
      if (!result.rows[0][0]) {
        console.error(`表 ${table} 不存在，跳过权限初始化`)
        return
      }
    }
    
    // 插入系统权限
    for (const [permName, permDesc] of Object.entries(SYSTEM_PERMISSIONS)) {
      const [resource, action] = permName.split(':')
      
      await client.queryArray(`
        INSERT INTO sexup_permissions (name, display_name, description, resource, action)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (name) DO NOTHING
      `, [permName, permDesc, permDesc, resource, action])
    }
    
    // 插入系统角色
    const roleDisplayNames = {
      [SYSTEM_ROLES.SUPER_ADMIN]: '超级管理员',
      [SYSTEM_ROLES.ADMIN]: '管理员',
      [SYSTEM_ROLES.MODERATOR]: '版主',
      [SYSTEM_ROLES.VIP_USER]: 'VIP用户',
      [SYSTEM_ROLES.REGULAR_USER]: '普通用户',
      [SYSTEM_ROLES.GUEST]: '访客'
    }
    
    for (const [roleName, displayName] of Object.entries(roleDisplayNames)) {
      await client.queryArray(`
        INSERT INTO sexup_roles (name, display_name, description, is_system)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (name) DO NOTHING
      `, [roleName, displayName, `系统预定义角色：${displayName}`, true])
    }
    
    // 分配角色权限
    for (const [roleName, permissions] of Object.entries(ROLE_PERMISSIONS)) {
      try {
        // 获取角色ID
        const roleResult = await client.queryObject(
          'SELECT id FROM sexup_roles WHERE name = $1',
          [roleName]
        )
        
        if (roleResult.rows.length === 0) {
          console.warn(`角色 ${roleName} 不存在，跳过权限分配`)
          continue
        }
        const roleId = (roleResult.rows[0] as any).id
        
        // 为角色分配权限
        for (const permName of permissions) {
          try {
            const permResult = await client.queryObject(
              'SELECT id FROM sexup_permissions WHERE name = $1',
              [permName]
            )
            
            if (permResult.rows.length === 0) {
              console.warn(`权限 ${permName} 不存在，跳过分配`)
              continue
            }
            const permId = (permResult.rows[0] as any).id
            
            await client.queryArray(`
              INSERT INTO sexup_role_permissions (role_id, permission_id)
              VALUES ($1, $2)
              ON CONFLICT (role_id, permission_id) DO NOTHING
            `, [roleId, permId])
          } catch (permError) {
            console.error(`分配权限 ${permName} 到角色 ${roleName} 失败:`, permError)
          }
        }
      } catch (roleError) {
        console.error(`处理角色 ${roleName} 权限分配失败:`, roleError)
      }
    }
    
    console.log('系统角色和权限初始化完成')
  } catch (error) {
    console.error('初始化系统角色和权限失败:', error)
    // 不抛出错误，让迁移继续进行
  }
}

/**
 * 初始化评估配置数据
 */
async function initializeEvaluationConfigs(client: any) {
  console.log('初始化评估配置数据...')

  try {
    // 先检查必要的表是否存在
    const tables = ['sexup_evaluation_styles', 'sexup_evaluation_perspectives', 'sexup_evaluation_details']
    for (const table of tables) {
      const result = await client.queryArray(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = $1
        )
      `, [table])
      
      if (!result.rows[0][0]) {
        console.error(`表 ${table} 不存在，跳过评估配置初始化`)
        return
      }
    }

    // 初始化评估风格
    const styles = [
      {
        code: 'CIVIL',
        name: '文明版本',
        description: '温和客观，适合大众',
        prompt_template: '请以客观、专业的态度评价这张图片。',
        icon: '🎭',
        color: 'blue',
        sort_order: 1
      },
      {
        code: 'PRAISE',
        name: '夸夸机',
        description: '正能量满满，积极鼓励',
        prompt_template: '作为一个超级正能量的夸夸机，请用最积极乐观的态度来评价这张图片。无论看到什么，都要找出积极的一面，给出鼓励和赞美。请用温暖、友善、充满正能量的语气回复。',
        icon: '🌟',
        color: 'yellow',
        sort_order: 2
      },
      {
        code: 'VULGAR',
        name: '直白版本',
        description: '直接不加修饰，真实表达',
        prompt_template: '请以直接、不加修饰的方式评价这张图片，可以使用更加直白的表达。',
        icon: '💥',
        color: 'red',
        sort_order: 3
      },
      {
        code: 'XBAOLOU',
        name: 'x报漏风格',
        description: '专业x报漏评估，深度分析图片特征',
        prompt_template: '作为专业的x报漏评估师，请从x报漏的专业角度深度分析这张图片的特征、质量和表现力。',
        icon: '🔥',
        color: 'orange',
        sort_order: 4
      }
    ]

    for (const style of styles) {
      try {
        await client.queryArray(`
          INSERT INTO sexup_evaluation_styles (code, name, description, prompt_template, icon, color, sort_order)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
          ON CONFLICT (code) DO UPDATE SET
            name = EXCLUDED.name,
            description = EXCLUDED.description,
            prompt_template = EXCLUDED.prompt_template,
            icon = EXCLUDED.icon,
            color = EXCLUDED.color,
            sort_order = EXCLUDED.sort_order
        `, [style.code, style.name, style.description, style.prompt_template, style.icon, style.color, style.sort_order])
      } catch (error) {
        console.error(`插入评估风格 ${style.code} 失败:`, error)
      }
    }

    // 初始化评估视角
    const perspectives = [
      {
        code: 'MALE',
        name: '男性视角',
        description: '从传统男性审美角度',
        prompt_template: '从传统男性审美角度评价这张图片。',
        icon: '👨',
        color: 'blue',
        sort_order: 1
      },
      {
        code: 'FEMALE',
        name: '女性视角',
        description: '从女性审美角度',
        prompt_template: '从女性审美角度评价这张图片。',
        icon: '👩',
        color: 'pink',
        sort_order: 2
      },
      {
        code: 'PHOTOGRAPHER',
        name: '摄影师',
        description: '专业摄影技术角度',
        prompt_template: '作为一名专业摄影师，从摄影技术角度（构图、光线、色彩、焦点等）评价这张图片。',
        icon: '📸',
        color: 'purple',
        sort_order: 3
      },
      {
        code: 'ARTIST',
        name: '艺术家',
        description: '艺术美学价值角度',
        prompt_template: '作为一名艺术家，从艺术角度（美学价值、创意表达、艺术感染力）评价这张图片。',
        icon: '🎨',
        color: 'indigo',
        sort_order: 4
      },
      {
        code: 'FOODIE',
        name: '美食家',
        description: '美食视觉呈现角度',
        prompt_template: '作为一名美食家，从美食角度（视觉呈现、摆盘艺术、食物诱人程度）评价这张图片。',
        icon: '🍽️',
        color: 'orange',
        sort_order: 5
      },
      {
        code: 'NATURE',
        name: '自然爱好者',
        description: '生态和自然角度',
        prompt_template: '作为一名自然爱好者，从生态和自然角度评价这张图片。',
        icon: '🌿',
        color: 'green',
        sort_order: 6
      },
      {
        code: 'TRAVEL',
        name: '旅行博主',
        description: '旅行和风景角度',
        prompt_template: '作为一名旅行博主，从旅行和风景角度评价这张图片。',
        icon: '✈️',
        color: 'cyan',
        sort_order: 7
      },
      {
        code: 'FASHION',
        name: '时尚达人',
        description: '时尚穿搭角度',
        prompt_template: '作为一名时尚达人，从时尚角度（穿搭、风格搭配、时尚品味）评价这张图片。',
        icon: '👗',
        color: 'rose',
        sort_order: 8
      },
      {
        code: 'XBAOLOU',
        name: 'x报漏视角',
        description: '从x报漏专业角度进行深度评估',
        prompt_template: '作为专业的x报漏评估师，从x报漏专业角度深度分析这张图片的各项特征和表现。',
        icon: '🔍',
        color: 'red',
        sort_order: 9
      }
    ]

    for (const perspective of perspectives) {
      await client.queryArray(`
        INSERT INTO sexup_evaluation_perspectives (code, name, description, prompt_template, icon, color, sort_order)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        ON CONFLICT (code) DO UPDATE SET
          name = EXCLUDED.name,
          description = EXCLUDED.description,
          prompt_template = EXCLUDED.prompt_template,
          icon = EXCLUDED.icon,
          color = EXCLUDED.color,
          sort_order = EXCLUDED.sort_order
      `, [perspective.code, perspective.name, perspective.description, perspective.prompt_template, perspective.icon, perspective.color, perspective.sort_order])
    }

    // 初始化详细程度
    const details = [
      {
        code: 'SMALL',
        name: '简洁',
        description: '简短评价（1-2句话）',
        prompt_template: '请给出简短的评价（1-2句话），直接说明主要特点。',
        icon: '📝',
        sort_order: 1
      },
      {
        code: 'MIDDEN',
        name: '详细',
        description: '中等详细分析（3-5句话）',
        prompt_template: '请给出中等详细的分析（3-5句话），从多个角度说明具体特点。',
        icon: '📄',
        sort_order: 2
      },
      {
        code: 'LARGE',
        name: '深度',
        description: '全面深度评估（8+句话）',
        prompt_template: '请给出全面深度的评估（8+句话），详细分析各个方面的特点和细节。',
        icon: '📚',
        sort_order: 3
      },
      {
        code: 'XBAOLOU',
        name: 'x报漏详细',
        description: 'x报漏专业级详细分析（全面深度）',
        prompt_template: '请从x报漏专业角度给出最详细的分析，包括所有相关特征、质量评估和专业建议。',
        icon: '🔬',
        sort_order: 4
      }
    ]

    for (const detail of details) {
      await client.queryArray(`
        INSERT INTO sexup_evaluation_details (code, name, description, prompt_template, icon, sort_order)
        VALUES ($1, $2, $3, $4, $5, $6)
        ON CONFLICT (code) DO UPDATE SET
          name = EXCLUDED.name,
          description = EXCLUDED.description,
          prompt_template = EXCLUDED.prompt_template,
          icon = EXCLUDED.icon,
          sort_order = EXCLUDED.sort_order
      `, [detail.code, detail.name, detail.description, detail.prompt_template, detail.icon, detail.sort_order])
    }

    console.log('评估配置数据初始化完成')
  } catch (error) {
    console.error('初始化评估配置数据失败:', error)
    // 不抛出错误，让迁移继续进行
  }
}
