import { getDbClient } from './database.ts'

/**
 * 完整UUID迁移脚本
 * 警告：这是一个复杂的迁移过程，建议在测试环境充分验证后再在生产环境执行
 */

interface MigrationStep {
  name: string
  execute: () => Promise<void>
  rollback: () => Promise<void>
}

// 迁移状态跟踪
export class UuidMigrationManager {
  private steps: MigrationStep[] = []
  private completedSteps: string[] = []

  constructor() {
    this.initializeSteps()
  }

  private initializeSteps() {
    this.steps = [
      {
        name: 'enable_uuid_extension',
        execute: this.enableUuidExtension,
        rollback: this.disableUuidExtension
      },
      {
        name: 'add_uuid_columns',
        execute: this.addUuidColumns,
        rollback: this.removeUuidColumns
      },
      {
        name: 'populate_uuid_values',
        execute: this.populateUuidValues,
        rollback: this.clearUuidValues
      },
      {
        name: 'create_uuid_indexes',
        execute: this.createUuidIndexes,
        rollback: this.dropUuidIndexes
      },
      {
        name: 'update_foreign_keys',
        execute: this.updateForeignKeys,
        rollback: this.revertForeignKeys
      },
      {
        name: 'switch_primary_keys',
        execute: this.switchPrimaryKeys,
        rollback: this.revertPrimaryKeys
      },
      {
        name: 'cleanup_serial_columns',
        execute: this.cleanupSerialColumns,
        rollback: this.restoreSerialColumns
      }
    ]
  }

  // 执行迁移
  async migrate() {
    console.log('开始完整UUID迁移...')
    
    for (const step of this.steps) {
      try {
        console.log(`执行步骤: ${step.name}`)
        await step.execute()
        this.completedSteps.push(step.name)
        console.log(`步骤 ${step.name} 完成`)
      } catch (error) {
        console.error(`步骤 ${step.name} 失败:`, error)
        await this.rollback()
        throw error
      }
    }
    
    console.log('UUID迁移完成')
  }

  // 回滚迁移
  async rollback() {
    console.log('开始回滚迁移...')
    
    // 按相反顺序回滚已完成的步骤
    for (let i = this.completedSteps.length - 1; i >= 0; i--) {
      const stepName = this.completedSteps[i]
      const step = this.steps.find(s => s.name === stepName)
      
      if (step) {
        try {
          console.log(`回滚步骤: ${stepName}`)
          await step.rollback()
          console.log(`步骤 ${stepName} 回滚完成`)
        } catch (error) {
          console.error(`回滚步骤 ${stepName} 失败:`, error)
        }
      }
    }
    
    this.completedSteps = []
    console.log('迁移回滚完成')
  }

  // 步骤1：启用UUID扩展
  private async enableUuidExtension() {
    const client = await getDbClient()
    try {
      await client.queryArray('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')
    } finally {
      await client.end()
    }
  }

  private async disableUuidExtension() {
    const client = await getDbClient()
    try {
      await client.queryArray('DROP EXTENSION IF EXISTS "uuid-ossp"')
    } finally {
      await client.end()
    }
  }

  // 步骤2：添加UUID列
  private async addUuidColumns() {
    const client = await getDbClient()
    try {
      const tables = [
        'sexup_users',
        'sexup_roles',
        'sexup_permissions', 
        'sexup_evaluations',
        'sexup_user_roles',
        'sexup_role_permissions'
      ]

      for (const table of tables) {
        await client.queryArray(`
          ALTER TABLE ${table} 
          ADD COLUMN IF NOT EXISTS uuid_id UUID DEFAULT uuid_generate_v4()
        `)
      }
    } finally {
      await client.end()
    }
  }

  private async removeUuidColumns() {
    const client = await getDbClient()
    try {
      const tables = [
        'sexup_users',
        'sexup_roles', 
        'sexup_permissions',
        'sexup_evaluations',
        'sexup_user_roles',
        'sexup_role_permissions'
      ]

      for (const table of tables) {
        await client.queryArray(`ALTER TABLE ${table} DROP COLUMN IF EXISTS uuid_id`)
      }
    } finally {
      await client.end()
    }
  }

  // 步骤3：填充UUID值
  private async populateUuidValues() {
    const client = await getDbClient()
    try {
      const tables = [
        'sexup_users',
        'sexup_roles',
        'sexup_permissions',
        'sexup_evaluations', 
        'sexup_user_roles',
        'sexup_role_permissions'
      ]

      for (const table of tables) {
        await client.queryArray(`
          UPDATE ${table} 
          SET uuid_id = uuid_generate_v4() 
          WHERE uuid_id IS NULL
        `)
      }
    } finally {
      await client.end()
    }
  }

  private async clearUuidValues() {
    const client = await getDbClient()
    try {
      const tables = [
        'sexup_users',
        'sexup_roles',
        'sexup_permissions',
        'sexup_evaluations',
        'sexup_user_roles', 
        'sexup_role_permissions'
      ]

      for (const table of tables) {
        await client.queryArray(`UPDATE ${table} SET uuid_id = NULL`)
      }
    } finally {
      await client.end()
    }
  }

  // 步骤4：创建UUID索引
  private async createUuidIndexes() {
    const client = await getDbClient()
    try {
      const tables = [
        'sexup_users',
        'sexup_roles',
        'sexup_permissions',
        'sexup_evaluations',
        'sexup_user_roles',
        'sexup_role_permissions'
      ]

      for (const table of tables) {
        await client.queryArray(`
          CREATE UNIQUE INDEX IF NOT EXISTS idx_${table}_uuid_id 
          ON ${table}(uuid_id)
        `)
      }
    } finally {
      await client.end()
    }
  }

  private async dropUuidIndexes() {
    const client = await getDbClient()
    try {
      const tables = [
        'sexup_users',
        'sexup_roles',
        'sexup_permissions', 
        'sexup_evaluations',
        'sexup_user_roles',
        'sexup_role_permissions'
      ]

      for (const table of tables) {
        await client.queryArray(`DROP INDEX IF EXISTS idx_${table}_uuid_id`)
      }
    } finally {
      await client.end()
    }
  }

  // 步骤5-7：更新外键、切换主键、清理（实现较复杂，需要详细设计）
  private async updateForeignKeys() {
    // 实现外键更新逻辑
    console.log('更新外键关系...')
  }

  private async revertForeignKeys() {
    // 实现外键回滚逻辑
    console.log('回滚外键关系...')
  }

  private async switchPrimaryKeys() {
    // 实现主键切换逻辑
    console.log('切换主键...')
  }

  private async revertPrimaryKeys() {
    // 实现主键回滚逻辑
    console.log('回滚主键...')
  }

  private async cleanupSerialColumns() {
    // 实现SERIAL列清理逻辑
    console.log('清理SERIAL列...')
  }

  private async restoreSerialColumns() {
    // 实现SERIAL列恢复逻辑
    console.log('恢复SERIAL列...')
  }
}

// 执行完整迁移
export async function executeFullUuidMigration() {
  const migrationManager = new UuidMigrationManager()
  
  try {
    await migrationManager.migrate()
    console.log('完整UUID迁移成功完成')
  } catch (error) {
    console.error('UUID迁移失败:', error)
    throw error
  }
}
