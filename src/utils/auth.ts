import { create, verify, getNumericDate } from 'djwt'
import { initializeUserPermissions, getUserPermissions } from './rbac.ts'

// JWT 密钥和配置
const JWT_SECRET = '8f8de7a749662780664cdaa39ed51e73'
const JWT_KEY = await crypto.subtle.importKey(
  'raw',
  new TextEncoder().encode(JWT_SECRET),
  { name: 'HMAC', hash: 'SHA-256' },
  false,
  ['sign', 'verify']
)

// 用户认证相关函数
export async function hashPassword(clientHashedPassword: string): Promise<string> {
  const encoder = new TextEncoder()
  // 对客户端已哈希的密码再次哈希，加上服务端盐
  const data = encoder.encode(clientHashedPassword + JWT_SECRET)
  const hashBuffer = await crypto.subtle.digest('SHA-256', data)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
}

export async function verifyPassword(clientHashedPassword: string, storedHash: string): Promise<boolean> {
  const hashedInput = await hashPassword(clientHashedPassword)
  return hashedInput === storedHash
}

// JWT token 生成（包含权限信息）
export async function generateJwtToken(userId: string | number | bigint, username: string): Promise<string> {
  // 将用户ID转换为字符串以支持BIGINT
  const userIdString = String(userId)

  // 获取用户权限信息（使用字符串ID）
  const userPermissions = await getUserPermissions(userIdString)

  const payload = {
    sub: userIdString, // 用户ID作为字符串
    username: username,
    roles: userPermissions.roles.map(r => r.name),
    permissions: userPermissions.permissions,
    iat: getNumericDate(new Date()),
    exp: getNumericDate(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)) // 7天过期
  }

  return await create({ alg: 'HS256', typ: 'JWT' }, payload, JWT_KEY)
}

// JWT token 验证
export async function verifyJwtToken(token: string): Promise<any> {
  try {
    const payload = await verify(token, JWT_KEY)
    return payload
  } catch (_error) {
    return null
  }
}

// 兼容旧的简化token（逐步迁移）
export function generateSimpleToken(userId: number | bigint, username: string): string {
  const payload = {
    userId: Number(userId),
    username,
    exp: Date.now() + (7 * 24 * 60 * 60 * 1000)
  }

  return btoa(JSON.stringify(payload)) + '.' + btoa(JWT_SECRET)
}

export function verifySimpleToken(token: string): any {
  try {
    const parts = token.split('.')
    if (parts.length !== 2) return null

    const payload = JSON.parse(atob(parts[0]))
    const signature = atob(parts[1])

    if (signature !== JWT_SECRET) return null
    if (payload.exp < Date.now()) return null

    return payload
  } catch (_error) {
    return null
  }
}

// 获取当前用户中间件
export async function getCurrentUser(c: any) {
  const authHeader = c.req.header('Authorization')
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null
  }

  const token = authHeader.substring(7)
  console.log('Token解析 - 原始token:', token.substring(0, 50) + '...')

  // 首先尝试验证JWT token
  let payload = await verifyJwtToken(token)
  console.log('JWT验证结果:', payload)

  // 如果JWT验证失败，尝试旧的简化token（向后兼容）
  if (!payload) {
    payload = verifySimpleToken(token)
    console.log('简单token验证结果:', payload)
    if (payload) {
      const user = {
        id: payload.userId,
        username: payload.username
      }
      console.log('返回简单token用户:', user)
      return user
    }
  } else {
    const user = {
      id: payload.sub, // 保持为字符串以支持BIGINT
      username: payload.username
    }
    console.log('返回JWT用户:', user, '原始sub:', payload.sub)
    return user
  }

  return null
}

// 用户注册后初始化权限
export async function initializeNewUserPermissions(userId: string | number): Promise<boolean> {
  return await initializeUserPermissions(userId)
}
