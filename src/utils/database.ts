import { Client } from '@postgres'
import { migrateRbacTables } from './rbac-migration.ts'
import { hashPassword } from './auth.ts'

// 数据库配置
const DATABASE_URL = "postgresql://yunhu:<EMAIL>:26257/supersex?sslmode=verify-full"

// 数据库连接函数
export async function getDbClient() {
  const client = new Client(DATABASE_URL)
  await client.connect()

  // 可选：抑制SERIAL主键警告
  try {
    await client.queryArray('SET sql_notices.enabled = false')
  } catch (_error) {
    // 忽略设置失败，继续正常操作
  }

  return client
}

// 初始化数据库表
export async function initDatabase() {
  const client = await getDbClient()
  try {
    // 启用UUID扩展
    await client.queryArray('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')

    console.log('检查并创建数据库表...')

    console.log('开始创建用户表...')
    // 创建用户表（使用UUID主键）
    await client.queryArray(`
      CREATE TABLE IF NOT EXISTS sexup_users (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        default_role_id UUID,
        reset_token VARCHAR(255),
        reset_token_expires TIMESTAMP,
        last_login_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `)
    console.log('用户表创建完成')

    console.log('开始创建分享表...')
    // 创建分享表
    await client.queryArray(`
      CREATE TABLE IF NOT EXISTS sexup_shares (
        id VARCHAR(8) PRIMARY KEY,
        user_id UUID REFERENCES sexup_users(id) ON DELETE CASCADE,
        data JSONB NOT NULL,
        is_public BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT NOW(),
        expires_at TIMESTAMP NOT NULL,
        view_count INTEGER DEFAULT 0
      )
    `)

    // 检查user_id字段是否允许NULL值，如果不允许则修改
    try {
      await client.queryArray(`ALTER TABLE sexup_shares ALTER COLUMN user_id DROP NOT NULL`)
    } catch (_error) {
      // 如果字段已经允许NULL，忽略错误
      console.log('user_id字段已经允许NULL值')
    }
    
    // 添加重置密码相关字段（如果不存在）
    try {
      await client.queryArray(`ALTER TABLE sexup_users ADD COLUMN IF NOT EXISTS reset_token VARCHAR(255)`)
      await client.queryArray(`ALTER TABLE sexup_users ADD COLUMN IF NOT EXISTS reset_token_expires TIMESTAMP`)
    } catch (error) {
      console.log('重置密码字段已存在或添加失败:', error)
    }
    
    console.log('分享表创建完成')
    
    // 创建索引
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_sexup_shares_expires_at ON sexup_shares(expires_at)
    `)
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_sexup_shares_user_id ON sexup_shares(user_id)
    `)
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_sexup_shares_public ON sexup_shares(is_public)
    `)
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_sexup_users_reset_token ON sexup_users(reset_token)
    `)
    
    console.log('基础数据库表初始化完成')

  } catch (error) {
    console.error('数据库初始化失败:', error)
    throw error
  } finally {
    await client.end()
  }

  // 在单独的连接中执行RBAC权限系统迁移
  console.log('开始执行RBAC权限系统迁移...')
  await migrateRbacTables()
  console.log('RBAC权限系统迁移完成')

  // 检查并创建系统角色用户
  console.log('检查系统角色用户...')
  await ensureSystemUsersExist()
  console.log('系统角色用户检查完成')



  console.log('数据库完整初始化完成')
}

// 清理过期的分享记录
export async function cleanupExpiredShares() {
  try {
    const client = await getDbClient()
    try {
      const result = await client.queryArray('DELETE FROM sexup_shares WHERE expires_at <= NOW()')
      console.log(`清理了 ${result.rowCount} 条过期记录`)
    } finally {
      await client.end()
    }
  } catch (error) {
    console.error('清理过期记录失败:', error)
  }
}

// 系统角色定义
const SYSTEM_ROLES = {
  SUPER_ADMIN: 'super_admin',     // 超级管理员
  ADMIN: 'admin',                 // 管理员
  MODERATOR: 'moderator',         // 版主
  VIP_USER: 'vip_user',          // VIP用户
  REGULAR_USER: 'regular_user',   // 普通用户
  GUEST: 'guest'                  // 访客
} as const

// 检查并创建系统角色用户
export async function ensureSystemUsersExist() {
  console.log('检查系统角色用户是否存在...')

  const client = await getDbClient()
  try {
    // 为每个系统角色创建对应用户
    for (const [_roleKey, roleName] of Object.entries(SYSTEM_ROLES)) {
      await createOrUpdateSystemUser(client, roleName)
    }

  } catch (error) {
    console.error('检查/创建系统用户失败:', error)
    throw error
  } finally {
    await client.end()
  }
}

// 创建或更新系统用户
async function createOrUpdateSystemUser(client: any, roleName: string) {
  try {
    const username = roleName
    const email = `${roleName}@sexup.com`
    const password = 'super@123456'

    // 在服务器端进行密码哈希
    const serverHashedPassword = await hashPassword(password)

    // 检查用户是否已存在
    const existingUser = await client.queryObject(`
      SELECT id, username, email FROM sexup_users WHERE username = $1
    `, [username])

    let userId: string

    if (existingUser.rows.length > 0) {
      // 用户已存在，强制更新信息
      const user = existingUser.rows[0] as any
      userId = user.id

      await client.queryArray(`
        UPDATE sexup_users
        SET email = $1, password_hash = $2, is_active = $3, updated_at = NOW()
        WHERE id = $4
      `, [email, serverHashedPassword, true, userId])

      console.log(`系统用户已更新: ${username} (${email})`)
    } else {
      // 创建新用户
      const createResult = await client.queryObject(`
        INSERT INTO sexup_users (username, email, password_hash, is_active, created_at, updated_at)
        VALUES ($1, $2, $3, $4, NOW(), NOW())
        RETURNING id, username, email
      `, [username, email, serverHashedPassword, true])

      const newUser = createResult.rows[0] as any
      userId = newUser.id
      console.log(`系统用户创建成功: ${newUser.username} (${newUser.email})`)
    }

    // 确保角色分配正确
    await ensureUserRoleAssignment(client, userId, roleName)

  } catch (error) {
    console.error(`处理系统用户 ${roleName} 失败:`, error)
  }
}

// 确保用户角色分配
async function ensureUserRoleAssignment(client: any, userId: string, roleName: string) {
  try {
    // 获取角色ID
    const roleResult = await client.queryObject(`
      SELECT id FROM sexup_roles WHERE name = $1
    `, [roleName])

    if (roleResult.rows.length === 0) {
      console.warn(`角色 ${roleName} 不存在，跳过角色分配`)
      return
    }

    const roleId = (roleResult.rows[0] as any).id

    // 检查是否已分配角色
    const existingAssignment = await client.queryObject(`
      SELECT id FROM sexup_user_roles WHERE user_id = $1 AND role_id = $2
    `, [userId, roleId])

    if (existingAssignment.rows.length === 0) {
      // 分配角色
      await client.queryArray(`
        INSERT INTO sexup_user_roles (user_id, role_id, assigned_by, assigned_at)
        VALUES ($1, $2, $1, NOW())
      `, [userId, roleId])

      console.log(`角色 ${roleName} 分配给用户成功`)
    }

    // 设置为默认角色（如果用户没有默认角色）
    const userInfo = await client.queryObject(`
      SELECT default_role_id FROM sexup_users WHERE id = $1
    `, [userId])

    if (userInfo.rows.length > 0) {
      const user = userInfo.rows[0] as any
      if (!user.default_role_id) {
        await client.queryArray(`
          UPDATE sexup_users SET default_role_id = $1 WHERE id = $2
        `, [roleId, userId])

        console.log(`设置 ${roleName} 为用户默认角色`)
      }
    }

  } catch (error) {
    console.error(`分配角色 ${roleName} 失败:`, error)
  }
}
