import { Client } from '@postgres'
import { migrateRbacTables } from './rbac-migration.ts'
import { hashPassword } from './auth.ts'
import { assignUserRole } from './rbac.ts'
import { migrateToUuidTables } from './uuid-migration.ts'

// 数据库配置
const DATABASE_URL = "postgresql://yunhu:<EMAIL>:26257/supersex?sslmode=verify-full"

// 数据库连接函数
export async function getDbClient() {
  const client = new Client(DATABASE_URL)
  await client.connect()

  // 可选：抑制SERIAL主键警告
  try {
    await client.queryArray('SET sql_notices.enabled = false')
  } catch (_error) {
    // 忽略设置失败，继续正常操作
  }

  return client
}

// 初始化数据库表
export async function initDatabase() {
  const client = await getDbClient()
  try {
    console.log('检查并创建数据库表...')

    console.log('开始创建用户表...')
    // 创建用户表（使用自增ID）
    await client.queryArray(`
      CREATE TABLE IF NOT EXISTS sexup_users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        default_role_id INTEGER,
        reset_token VARCHAR(255),
        reset_token_expires TIMESTAMP,
        last_login_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `)
    console.log('用户表创建完成')

    console.log('开始创建分享表...')
    // 创建分享表
    await client.queryArray(`
      CREATE TABLE IF NOT EXISTS sexup_shares (
        id VARCHAR(8) PRIMARY KEY,
        user_id INTEGER REFERENCES sexup_users(id) ON DELETE CASCADE,
        data JSONB NOT NULL,
        is_public BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT NOW(),
        expires_at TIMESTAMP NOT NULL,
        view_count INTEGER DEFAULT 0
      )
    `)

    // 检查user_id字段是否允许NULL值，如果不允许则修改
    try {
      await client.queryArray(`ALTER TABLE sexup_shares ALTER COLUMN user_id DROP NOT NULL`)
    } catch (_error) {
      // 如果字段已经允许NULL，忽略错误
      console.log('user_id字段已经允许NULL值')
    }
    
    // 添加重置密码相关字段（如果不存在）
    try {
      await client.queryArray(`ALTER TABLE sexup_users ADD COLUMN IF NOT EXISTS reset_token VARCHAR(255)`)
      await client.queryArray(`ALTER TABLE sexup_users ADD COLUMN IF NOT EXISTS reset_token_expires TIMESTAMP`)
    } catch (error) {
      console.log('重置密码字段已存在或添加失败:', error)
    }
    
    console.log('分享表创建完成')
    
    // 创建索引
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_sexup_shares_expires_at ON sexup_shares(expires_at)
    `)
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_sexup_shares_user_id ON sexup_shares(user_id)
    `)
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_sexup_shares_public ON sexup_shares(is_public)
    `)
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_sexup_users_reset_token ON sexup_users(reset_token)
    `)
    
    console.log('基础数据库表初始化完成')

  } catch (error) {
    console.error('数据库初始化失败:', error)
    throw error
  } finally {
    await client.end()
  }

  // 在单独的连接中执行RBAC权限系统迁移
  console.log('开始执行RBAC权限系统迁移...')
  await migrateRbacTables()
  console.log('RBAC权限系统迁移完成')

  // 检查并创建超级管理员用户
  console.log('检查超级管理员用户...')
  await ensureSuperAdminExists()
  console.log('超级管理员用户检查完成')

  // 可选：执行UUID表迁移（新表使用UUID）
  try {
    console.log('开始UUID表迁移...')
    // 只启用UUID扩展和创建新表，避免现有表的回填问题
    await enableUuidExtension()
    await createUuidTables()
    console.log('UUID表迁移完成')
  } catch (error) {
    console.warn('UUID表迁移失败，继续使用SERIAL主键:', error)
  }

  console.log('数据库完整初始化完成')
}

// 清理过期的分享记录
export async function cleanupExpiredShares() {
  try {
    const client = await getDbClient()
    try {
      const result = await client.queryArray('DELETE FROM sexup_shares WHERE expires_at <= NOW()')
      console.log(`清理了 ${result.rowCount} 条过期记录`)
    } finally {
      await client.end()
    }
  } catch (error) {
    console.error('清理过期记录失败:', error)
  }
}

// 检查并创建超级管理员用户
export async function ensureSuperAdminExists() {
  const client = await getDbClient()
  try {
    console.log('检查超级管理员用户是否存在...')

    // 检查用户是否存在
    const userResult = await client.queryObject(
      'SELECT id, username, email FROM sexup_users WHERE username = $1 OR email = $2',
      ['super', '<EMAIL>']
    )

    if (userResult.rows.length > 0) {
      const existingUser = userResult.rows[0] as any
      console.log(`超级管理员用户已存在: ${existingUser.username} (${existingUser.email}), 当前ID: ${existingUser.id}`)

      // 如果用户ID不是99，需要更新
      if (Number(existingUser.id) !== 99) {
        console.log('超级管理员用户ID不是99，开始更新...')

        try {
          // 首先检查ID 99是否已被占用
          const id99Check = await client.queryObject('SELECT id FROM sexup_users WHERE id = 99')
          if (id99Check.rows.length > 0) {
            console.log('ID 99已被其他用户占用，先删除或移动该用户')
            // 将占用ID 99的用户移动到一个临时ID
            await client.queryArray('UPDATE sexup_users SET id = 9999 WHERE id = 99')
          }

          // 在事务中更新用户ID和相关外键
          await client.queryArray('BEGIN')

          try {
            // 更新相关表中的外键引用
            await client.queryArray('UPDATE sexup_evaluations SET user_id = 99 WHERE user_id = $1', [existingUser.id])
            await client.queryArray('UPDATE sexup_shares SET user_id = 99 WHERE user_id = $1', [existingUser.id])
            await client.queryArray('UPDATE sexup_user_roles SET user_id = 99 WHERE user_id = $1', [existingUser.id])

            // 最后更新用户表的ID
            await client.queryArray('UPDATE sexup_users SET id = 99 WHERE id = $1', [existingUser.id])

            await client.queryArray('COMMIT')
          } catch (updateError) {
            await client.queryArray('ROLLBACK')
            throw updateError
          }

          console.log(`超级管理员用户ID已更新为99 (原ID: ${existingUser.id})`)
        } catch (error) {
          console.error('更新超级管理员用户ID失败:', error)
          // 如果更新失败，继续使用原ID
        }
      }

      // 确保用户有超级管理员角色（使用ID 99）
      await assignUserRole('99', 'super_admin')
      console.log('已确保超级管理员角色分配')
      return
    }

    console.log('超级管理员用户不存在，开始创建...')

    // 客户端密码哈希 (模拟前端的哈希过程)
    const clientPassword = '123456'
    const encoder = new TextEncoder()
    const clientSalt = 'client-salt-2024'
    const clientData = encoder.encode(clientPassword + clientSalt)
    const clientHashBuffer = await crypto.subtle.digest('SHA-256', clientData)
    const clientHashArray = Array.from(new Uint8Array(clientHashBuffer))
    const clientHashedPassword = clientHashArray.map(b => b.toString(16).padStart(2, '0')).join('')

    // 服务端密码哈希
    const serverHashedPassword = await hashPassword(clientHashedPassword)

    // 创建超级管理员用户，指定ID为99
    const createResult = await client.queryObject(`
      INSERT INTO sexup_users (id, username, email, password_hash, is_active, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
      RETURNING id, username, email
    `, [99, 'super', '<EMAIL>', serverHashedPassword, true])

    const newUser = createResult.rows[0] as any
    console.log(`超级管理员用户创建成功: ${newUser.username} (ID: ${newUser.id})`)

    // 分配超级管理员角色
    await assignUserRole(String(newUser.id), 'super_admin')
    console.log('超级管理员角色分配成功')

  } catch (error) {
    console.error('检查/创建超级管理员失败:', error)
    throw error
  } finally {
    await client.end()
  }
}
