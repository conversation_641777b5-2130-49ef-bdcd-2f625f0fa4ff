// 生成8位分享ID
export function generateShareId(): string {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// 错误页面模板
export function getErrorPage(title: string, message: string): string {
  return `
      <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${title} - AI图片评估助手</title>
        <script src="https://cdn.tailwindcss.com"></script>
        </head>
      <body class="bg-gray-50 min-h-screen flex items-center justify-center p-4">
        <div class="text-center">
          <div class="bg-white rounded-lg shadow-lg p-8 max-w-md mx-auto">
            <div class="text-red-500 text-6xl mb-4">😵</div>
            <h1 class="text-2xl font-bold text-gray-800 mb-2">${title}</h1>
            <p class="text-gray-600 mb-6">${message}</p>
            <a href="/" class="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
              返回首页
            </a>
          </div>
        </div>
        </body>
      </html>
  `
}

// 分享页面HTML模板
export const SHARE_PAGE_TEMPLATE = (shareData: any, shareId?: string) => `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分享的评估结果 - AI图片评估助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <meta property="og:title" content="上不上AI评分系统 - 评估结果分享">
    <meta property="og:description" content="查看这个有趣的AI评估结果：${shareData.verdict} (${shareData.rating}/100)">
    <meta property="og:type" content="website">
    <style>
        body { 
            font-family: 'Inter', sans-serif; 
            -webkit-overflow-scrolling: touch;
            -webkit-tap-highlight-color: transparent;
        }
        .share-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 1rem;
            padding: 1.5rem;
            color: white;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        @media (min-width: 768px) {
            .share-card { padding: 2rem; }
        }
        .result-card {
            background: white;
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-top: 1.5rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        @media (min-width: 768px) {
            .result-card { 
                padding: 2rem; 
                margin-top: 2rem;
            }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen py-4 px-4">
    <div class="container mx-auto max-w-2xl">
        <div class="share-card text-center">
            <h1 class="text-2xl md:text-3xl font-bold mb-4">🤖 AI专业评估结果</h1>
            <p class="text-base md:text-lg opacity-90">来自"AI图片评估助手"的专业多视角评估</p>
        </div>

        <div class="result-card">
            <div class="text-center mb-6">
                <div class="text-3xl md:text-4xl font-bold ${shareData.verdict === '上' ? 'text-blue-600' : 'text-red-600'} mb-2">
                    ${shareData.verdict}
                </div>
                <div class="text-xl md:text-2xl text-gray-600">
                    评分: ${shareData.rating}/100
                    ${shareData.verdict === '上' ? (shareData.rating >= 7 ? '👍' : '😐') : (shareData.rating <= 3 ? '👎' : '🤔')}
                </div>
            </div>

            ${shareData.image ? `
            <div class="mb-6 text-center">
                <img src="${shareData.image}" alt="评估图片" class="max-w-full h-auto rounded-lg mx-auto" style="max-height: 250px;">
            </div>
            ` : ''}

            <div class="bg-gray-50 rounded-lg p-3 md:p-4 mb-6">
                <h3 class="font-semibold text-gray-800 mb-2">AI评估说明：</h3>
                <p class="text-gray-700 leading-relaxed whitespace-pre-wrap text-sm md:text-base">${shareData.explanation}</p>
            </div>

            <div class="text-xs md:text-sm text-gray-500 mb-6">
                <p>评估模式: ${shareData.promptMode || '未知模式'}</p>
                <p>评估时间: ${shareData.timestamp}</p>
                ${shareData.viewCount ? `<p>已被浏览 ${shareData.viewCount} 次</p>` : ''}
                ${shareId ? `<p>分享ID: ${shareId}</p>` : ''}
            </div>

            <div class="text-center">
                <a href="/" class="inline-flex items-center px-4 md:px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm md:text-base">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414-1.414L9 5.586 7.707 4.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0z" clip-rule="evenodd" />
                    </svg>
                    我也要试试
                </a>
            </div>
        </div>

        <div class="text-center mt-8 text-gray-500">
            <p class="text-xs md:text-sm">💡 AI图片评估助手：专业多视角评估，夸夸机正能量模式</p>
        </div>
    </div>
</body>
</html>` 
