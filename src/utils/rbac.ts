import { getDbClient } from './database.ts'
import { SYSTEM_ROLES } from './rbac-migration.ts'

/**
 * RBAC权限系统核心功能
 */

// 权限检查结果接口
export interface PermissionCheckResult {
  allowed: boolean
  reason?: string
  requiredPermissions?: string[]
  userPermissions?: string[]
}

// 用户权限信息接口
export interface UserPermissions {
  userId: string
  roles: Array<{
    id: string
    name: string
    displayName: string
  }>
  permissions: string[]
}

/**
 * 获取用户的所有权限
 */
export async function getUserPermissions(userId: string | number): Promise<UserPermissions> {
  const userIdString = String(userId)
  const client = await getDbClient()

  try {
    // 获取用户的所有角色
    const rolesResult = await client.queryObject(`
      SELECT r.id, r.name, r.display_name
      FROM sexup_roles r
      INNER JOIN sexup_user_roles ur ON r.id = ur.role_id
      WHERE ur.user_id = $1
        AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
    `, [userIdString])

    const roles = rolesResult.rows.map((row: any) => ({
      id: String(row.id),
      name: row.name,
      displayName: row.display_name
    }))

    // 获取用户的所有权限（通过角色）
    const permissionsResult = await client.queryObject(`
      SELECT DISTINCT p.name
      FROM sexup_permissions p
      INNER JOIN sexup_role_permissions rp ON p.id = rp.permission_id
      INNER JOIN sexup_user_roles ur ON rp.role_id = ur.role_id
      WHERE ur.user_id = $1
        AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
    `, [userIdString])

    const permissions = permissionsResult.rows.map((row: any) => row.name)

    return {
      userId: userIdString,
      roles,
      permissions
    }
  } finally {
    await client.end()
  }
}

/**
 * 检查用户是否具有指定权限
 */
export async function checkUserPermission(
  userId: string | number,
  requiredPermissions: string | string[]
): Promise<PermissionCheckResult> {
  const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions]
  
  try {
    const userPerms = await getUserPermissions(userId)
    
    // 检查是否具有所有必需权限
    const missingPermissions = permissions.filter(perm => !userPerms.permissions.includes(perm))
    
    if (missingPermissions.length === 0) {
      return { allowed: true }
    }
    
    return {
      allowed: false,
      reason: `缺少权限: ${missingPermissions.join(', ')}`,
      requiredPermissions: permissions,
      userPermissions: userPerms.permissions
    }
  } catch (error) {
    return {
      allowed: false,
      reason: `权限检查失败: ${error instanceof Error ? error.message : '未知错误'}`
    }
  }
}

/**
 * 检查用户是否具有指定角色
 */
export async function checkUserRole(userId: string | number, requiredRoles: string | string[]): Promise<boolean> {
  const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles]
  
  try {
    const userPerms = await getUserPermissions(userId)
    const userRoleNames = userPerms.roles.map(role => role.name)
    
    return roles.some(role => userRoleNames.includes(role))
  } catch (error) {
    console.error('角色检查失败:', error)
    return false
  }
}

/**
 * 为用户分配角色
 */
export async function assignUserRole(
  userId: string | number,
  roleName: string,
  assignedBy?: string | number,
  expiresAt?: Date
): Promise<boolean> {
  const client = await getDbClient()

  try {
    const userIdString = String(userId)
    const assignedByString = assignedBy ? String(assignedBy) : null

    // 获取角色ID
    const roleResult = await client.queryObject(
      'SELECT id FROM sexup_roles WHERE name = $1',
      [roleName]
    )

    if (roleResult.rows.length === 0) {
      throw new Error(`角色不存在: ${roleName}`)
    }

    const roleId = String((roleResult.rows[0] as any).id)
    
    // 分配角色
    await client.queryArray(`
      INSERT INTO sexup_user_roles (user_id, role_id, assigned_by, expires_at)
      VALUES ($1, $2, $3, $4)
      ON CONFLICT (user_id, role_id)
      DO UPDATE SET
        assigned_by = EXCLUDED.assigned_by,
        assigned_at = NOW(),
        expires_at = EXCLUDED.expires_at
    `, [userIdString, roleId, assignedByString, expiresAt])
    
    return true
  } catch (error) {
    console.error('角色分配失败:', error)
    return false
  } finally {
    await client.end()
  }
}

/**
 * 移除用户角色
 */
export async function removeUserRole(userId: string | number, roleName: string): Promise<boolean> {
  const client = await getDbClient()

  try {
    const userIdString = String(userId)
    const result = await client.queryArray(`
      DELETE FROM sexup_user_roles
      WHERE user_id = $1
        AND role_id = (SELECT id FROM sexup_roles WHERE name = $2)
    `, [userIdString, roleName])

    return (result.rowCount || 0) > 0
  } catch (error) {
    console.error('角色移除失败:', error)
    return false
  } finally {
    await client.end()
  }
}

/**
 * 获取用户的默认角色（新用户注册时使用）
 */
export function getDefaultUserRole(): string {
  return SYSTEM_ROLES.MODERATOR
}

/**
 * 初始化新用户的默认权限
 */
export async function initializeUserPermissions(userId: string | number): Promise<boolean> {
  try {
    const defaultRole = getDefaultUserRole()
    return await assignUserRole(userId, defaultRole)
  } catch (error) {
    console.error('用户权限初始化失败:', error)
    return false
  }
}

/**
 * 权限中间件工厂函数
 */
export function requirePermissions(permissions: string | string[]) {
  return async (c: any, next: any) => {
    const user = c.get('user')

    if (!user) {
      return c.json({ error: '未登录' }, 401)
    }

    console.log('权限检查 - 用户信息:', { user, permissions })
    const permissionCheck = await checkUserPermission(user.id, permissions)
    console.log('权限检查结果:', permissionCheck)

    if (!permissionCheck.allowed) {
      return c.json({
        error: '权限不足',
        details: permissionCheck.reason
      }, 403)
    }

    await next()
  }
}

/**
 * 角色中间件工厂函数
 */
export function requireRoles(roles: string | string[]) {
  return async (c: any, next: any) => {
    const user = c.get('user')
    
    if (!user) {
      return c.json({ error: '未登录' }, 401)
    }
    
    const hasRole = await checkUserRole(user.id, roles)
    
    if (!hasRole) {
      const roleList = Array.isArray(roles) ? roles.join(', ') : roles
      return c.json({ 
        error: '权限不足', 
        details: `需要角色: ${roleList}` 
      }, 403)
    }
    
    await next()
  }
}

/**
 * 资源所有权检查中间件
 */
export function requireOwnership(resourceIdParam: string = 'id') {
  return async (c: any, next: any) => {
    const user = c.get('user')
    const resourceId = c.req.param(resourceIdParam)
    
    if (!user) {
      return c.json({ error: '未登录' }, 401)
    }
    
    // 检查是否是管理员（管理员可以访问所有资源）
    const isAdmin = await checkUserRole(user.id, [SYSTEM_ROLES.ADMIN, SYSTEM_ROLES.SUPER_ADMIN])
    if (isAdmin) {
      await next()
      return
    }
    
    // 这里需要根据具体资源类型实现所有权检查
    // 暂时允许通过，具体实现在各个路由中处理
    c.set('requireOwnershipCheck', { userId: user.id, resourceId })
    await next()
  }
}
