import { getDbClient } from './database.ts'

/**
 * 抑制SERIAL主键警告的工具函数
 * 注意：这只是临时方案，不能解决根本的性能问题
 */

// 设置会话级别的警告抑制
export async function suppressSerialWarnings() {
  const client = await getDbClient()
  try {
    // 在CockroachDB中设置会话参数来抑制特定警告
    await client.queryArray(`
      SET sql_notices.enabled = false
    `)
    
    console.log('已抑制SERIAL主键警告（仅当前会话）')
  } catch (error) {
    console.warn('无法抑制SERIAL警告:', error)
  } finally {
    await client.end()
  }
}

// 创建一个包装函数，自动抑制警告
export async function executeWithSuppressedWarnings<T>(
  operation: () => Promise<T>
): Promise<T> {
  await suppressSerialWarnings()
  return await operation()
}

// 修改数据库客户端，自动抑制警告
export async function getDbClientWithSuppressedWarnings() {
  const client = await getDbClient()
  
  try {
    await client.queryArray('SET sql_notices.enabled = false')
  } catch (error) {
    // 忽略设置失败的错误
    console.warn('无法设置警告抑制:', error)
  }
  
  return client
}

// 为现有代码提供兼容性包装
export { getDbClientWithSuppressedWarnings as getDbClient }
