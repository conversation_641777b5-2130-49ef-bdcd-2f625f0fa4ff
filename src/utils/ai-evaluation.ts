// AI评估服务
import type { ApiConfig } from '../types/index.ts'

export interface EvaluationParams {
  imageUrl: string
  style: string
  perspective: string
  detail: string
  apiConfig?: ApiConfig
}

export interface EvaluationResult {
  verdict: string
  rating: number
  explanation: string
  timestamp: string
}

// 默认API配置
const DEFAULT_API_CONFIG: ApiConfig = {
  baseUrl: 'https://zone.veloera.org/v1/chat/completions',
  apiKey: 'sk-wm9Qw1dvOo9O9Wu0oenj45zv4EBNapBA3c04Rd3KIhRNH0eB',
  model: 'gpt-4.1-mini'
}

// 风格映射
const STYLE_MAPPING = {
  'civilized': '文明版本',
  'praise': '夸夸机模式',
  'vulgar': '重口味版本'
}

// 视角映射
const PERSPECTIVE_MAPPING = {
  'male': '男性视角',
  'female': '女性视角',
  'photographer': '摄影师视角',
  'artist': '艺术家视角',
  'foodie': '美食家视角',
  'nature': '自然爱好者视角',
  'travel': '旅行者视角',
  'fashion': '时尚达人视角'
}


// 构建评估提示词
function buildPrompt(style: string, perspective: string, detail: string): string {
  const perspectiveText = PERSPECTIVE_MAPPING[perspective as keyof typeof PERSPECTIVE_MAPPING] || '男性视角'

  let basePrompt = `你是一个专业的图片评估助手，请从${perspectiveText}对这张图片进行评估。`

  // 根据风格调整提示词
  if (style === 'praise') {
    basePrompt += `
请用夸夸机模式，给出满满的正能量评价：
- 发现图片中的所有美好之处
- 用积极正面的语言表达
- 给予鼓励和赞美
- 评分偏向较高分数（70-100分）`
  } else if (style === 'vulgar') {
    basePrompt += `
请用重口味模式进行评估：
- 可以使用更直接的表达方式
- 评价标准可以更严格
- 允许使用网络流行语
- 评分范围正常（1-100分）`
  } else {
    basePrompt += `
请用文明礼貌的方式进行评估：
- 客观公正地分析图片
- 使用专业术语
- 给出建设性建议
- 评分公正合理（1-100分）`
  }

  // 根据详细程度调整
  if (detail === 'detailed') {
    basePrompt += `
请提供详细的评估：
- 分析构图、色彩、光线等技术要素
- 评价主题表达和情感传达
- 给出具体的改进建议
- 字数控制在200-300字`
  } else if (detail === 'moderate') {
    basePrompt += `
请提供适中的评估：
- 重点分析几个关键要素
- 简要说明优缺点
- 给出主要建议
- 字数控制在100-200字`
  } else {
    basePrompt += `
请提供简洁的评估：
- 快速总结图片特点
- 给出核心评价
- 简短建议
- 字数控制在50-100字`
  }

  basePrompt += `

请按照以下JSON格式返回评估结果：
{
  "verdict": "上/中/下",
  "rating": 数字评分(1-100),
  "explanation": "详细评估说明"
}

注意：
1. verdict只能是"上"、"中"、"下"三个选项之一
2. rating必须是1-100之间的整数
3. explanation要符合上述要求的详细程度
4. 请确保返回的是有效的JSON格式`

  return basePrompt
}

// 调用AI API进行评估
export async function evaluateImage(params: EvaluationParams): Promise<EvaluationResult> {
  const config = { ...DEFAULT_API_CONFIG, ...params.apiConfig }
  const prompt = buildPrompt(params.style, params.perspective, params.detail)

  try {
    console.log('开始AI评估，配置:', {
      baseUrl: config.baseUrl,
      model: config.model,
      style: params.style,
      perspective: params.perspective,
      detail: params.detail
    })

    const response = await fetch(config.baseUrl!, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`
      },
      body: JSON.stringify({
        model: config.model,
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: prompt
              },
              {
                type: 'image_url',
                image_url: {
                  url: params.imageUrl
                }
              }
            ]
          }
        ],
        max_tokens: 1000,
        temperature: 0.7
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('AI API调用失败:', response.status, errorText)
      throw new Error(`AI API调用失败: ${response.status} ${errorText}`)
    }

    const data = await response.json()
    console.log('AI API响应:', data)

    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      throw new Error('AI API返回格式错误')
    }

    const content = data.choices[0].message.content
    console.log('AI返回内容:', content)

    // 尝试解析JSON响应
    let evaluationData
    try {
      // 提取JSON部分（可能包含在代码块中）
      const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/) || content.match(/\{[\s\S]*\}/)
      const jsonStr = jsonMatch ? (jsonMatch[1] || jsonMatch[0]) : content
      evaluationData = JSON.parse(jsonStr)
    } catch (parseError) {
      console.error('解析AI响应JSON失败:', parseError, '原始内容:', content)
      
      // 如果JSON解析失败，尝试从文本中提取信息
      const verdictMatch = content.match(/(?:verdict|评价|结论)[:：]\s*["']?([上中下])["']?/i)
      const ratingMatch = content.match(/(?:rating|评分|分数)[:：]\s*(\d+)/i)
      
      evaluationData = {
        verdict: verdictMatch ? verdictMatch[1] : '中',
        rating: ratingMatch ? parseInt(ratingMatch[1]) : 75,
        explanation: content.length > 500 ? content.substring(0, 500) + '...' : content
      }
    }

    // 验证和标准化数据
    const result: EvaluationResult = {
      verdict: ['上', '中', '下'].includes(evaluationData.verdict) ? evaluationData.verdict : '中',
      rating: Math.max(1, Math.min(100, parseInt(evaluationData.rating) || 75)),
      explanation: evaluationData.explanation || '评估完成',
      timestamp: new Date().toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    console.log('最终评估结果:', result)
    return result

  } catch (error: any) {
    console.error('AI评估失败:', error)
    
    // 返回备用结果
    return {
      verdict: '中',
      rating: 75,
      explanation: `评估服务暂时不可用，请稍后重试。错误信息: ${error.message}`,
      timestamp: new Date().toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }
}
