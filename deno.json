{"name": "sexupaievaluation", "version": "1.0.0", "description": "AI图片评估助手 - 专业多视角评估工具", "exports": "./main.ts", "tasks": {"dev": "deno run --allow-net --allow-read --allow-env --watch --unstable-kv main.ts", "start": "deno run --allow-net --allow-read --allow-env --unstable-kv main.ts", "serve": "deno serve --allow-net --allow-read --allow-env main.ts", "check": "deno check main.ts", "deploy": "deployctl deploy --prod"}, "imports": {"hono": "jsr:@hono/hono@^4.8.0", "hono/cors": "jsr:@hono/hono@^4.8.0/cors", "hono/deno": "jsr:@hono/hono@^4.8.0/deno", "@postgres": "https://deno.land/x/postgres@v0.17.0/mod.ts", "djwt": "https://deno.land/x/djwt@v3.0.2/mod.ts"}, "compilerOptions": {"lib": ["deno.window", "deno.ns", "es2022"], "strict": true, "jsx": "react-jsx", "jsxImportSource": "hono/jsx"}, "fmt": {"useTabs": false, "lineWidth": 80, "indentWidth": 2, "semiColons": false, "singleQuote": true, "proseWrap": "preserve", "include": ["src/", "main.ts"], "exclude": ["static/"]}, "lint": {"include": ["src/", "main.ts"], "exclude": ["static/"], "rules": {"tags": ["recommended"], "exclude": ["no-explicit-any"]}}, "deploy": {"project": "e79727e7-d3d7-4d68-9ce1-ad6bf422dc5a", "exclude": ["**/node_modules"], "include": [], "entrypoint": "main.ts"}}