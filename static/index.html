<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0">
    <title>AI图片评估助手 - 专业多视角评估工具</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="AI图片评估助手，专业的多视角图片评估工具。支持夸夸机模式给你满满正能量，还有摄影师、美食家、艺术家等8种专业视角。上传任何图片，获得专业评估和建议。完全免费使用。">
    <meta name="keywords" content="AI图片评估,夸夸机,正能量评价,专业摄影评估,美食评价,艺术评估,AI工具,免费AI,多视角分析,图片分析助手">
    <meta name="author" content="上不上AI评分系统">
    <meta name="robots" content="index, follow">
    <meta name="googlebot" content="index, follow">
    <meta name="bingbot" content="index, follow">
    <meta name="language" content="zh-CN">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://sexup.deno.dev/">
    <meta property="og:title" content="AI图片评估助手 - 专业多视角评估工具">
    <meta property="og:description" content="专业的AI图片评估工具，支持夸夸机正能量模式、摄影师、美食家等8种专业视角。上传任何图片，获得专业评估建议。">
    <meta property="og:image" content="https://sexup.deno.dev/og-image.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:locale" content="zh_CN">
    <meta property="og:site_name" content="AI图片评估助手">
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://sexup.deno.dev/">
    <meta name="twitter:title" content="AI图片评估助手 - 专业多视角评估工具">
    <meta name="twitter:description" content="支持夸夸机正能量模式、摄影师、美食家等专业视角的AI图片评估工具。完全免费使用。">
    <meta name="twitter:image" content="https://sexup.deno.dev/og-image.png">
    
    <!-- Additional SEO -->
    <meta name="theme-color" content="#FF6900">
    <link rel="canonical" href="https://sexup.deno.dev/">

    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <link rel="icon" href="https://dash.deno.com/assets/logo.svg">

    <!-- 小米风格字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- 小米风格配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'xiaomi-orange': '#FF6900',
                        'xiaomi-blue': '#0084FF',
                        'xiaomi-gray': '#F5F5F5',
                        'xiaomi-dark': '#1A1A1A',
                        'xiaomi-text': '#333333',
                        'xiaomi-light': '#FAFAFA'
                    },
                    fontFamily: {
                        'xiaomi': ['Inter', 'system-ui', '-apple-system', 'sans-serif']
                    },
                    borderRadius: {
                        'xiaomi': '12px',
                        'xiaomi-lg': '16px',
                        'xiaomi-xl': '20px'
                    },
                    boxShadow: {
                        'xiaomi': '0 2px 20px rgba(0, 0, 0, 0.08)',
                        'xiaomi-lg': '0 8px 40px rgba(0, 0, 0, 0.12)',
                        'xiaomi-hover': '0 4px 30px rgba(0, 0, 0, 0.15)'
                    }
                }
            }
        }
    </script>
    
    <!-- 结构化数据 / Schema.org -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "AI图片评估助手",
        "description": "专业的多视角AI图片评估工具，支持夸夸机正能量模式、摄影师视角、美食家视角等8种专业评估方式",
        "url": "https://sexup.deno.dev/",
        "applicationCategory": "MultimediaApplication",
        "operatingSystem": "All",
        "featureList": [
            "夸夸机正能量评估",
            "多专业视角分析",
            "摄影师技术评估",
            "美食专业评价",
            "艺术审美分析",
            "自然生态评估",
            "时尚潮流分析",
            "旅游风景评价"
        ],
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "CNY"
        },
        "author": {
            "@type": "Organization",
            "name": "AI图片评估助手"
        },
        "provider": {
            "@type": "Organization",
            "name": "AI图片评估助手",
            "url": "https://sexup.deno.dev/"
        }
    }
    </script>

    <style>
        /* 小米风格自定义样式 */
        .xiaomi-gradient {
            background: linear-gradient(135deg, #FF6900 0%, #FF8F00 100%);
        }

        .xiaomi-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .xiaomi-card:hover {
            box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }

        .xiaomi-button {
            background: linear-gradient(135deg, #FF6900 0%, #FF8F00 100%);
            border: none;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 20px rgba(255, 105, 0, 0.3);
        }

        .xiaomi-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 30px rgba(255, 105, 0, 0.4);
        }

        .xiaomi-button:active {
            transform: translateY(0);
        }

        .xiaomi-input {
            border: 2px solid #E5E5E5;
            border-radius: 12px;
            padding: 16px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: white;
        }

        .xiaomi-input:focus {
            border-color: #FF6900;
            outline: none;
            box-shadow: 0 0 0 4px rgba(255, 105, 0, 0.1);
        }

        .xiaomi-option {
            background: white;
            border: 2px solid #E5E5E5;
            border-radius: 12px;
            padding: 16px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .xiaomi-option:hover {
            border-color: #FF6900;
            background: #FFF5F0;
        }

        .xiaomi-option.selected {
            border-color: #FF6900;
            background: linear-gradient(135deg, #FF6900 0%, #FF8F00 100%);
            color: white;
        }

        .xiaomi-upload-area {
            border: 2px dashed #E5E5E5;
            border-radius: 16px;
            background: #FAFAFA;
            transition: all 0.3s ease;
        }

        .xiaomi-upload-area:hover {
            border-color: #FF6900;
            background: #FFF5F0;
        }

        .xiaomi-upload-area.dragover {
            border-color: #FF6900;
            background: #FFF5F0;
            transform: scale(1.02);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .xiaomi-card {
                border-radius: 12px;
            }

            .xiaomi-button {
                border-radius: 10px;
            }

            .xiaomi-input {
                border-radius: 10px;
                padding: 14px;
            }

            .xiaomi-option {
                border-radius: 10px;
                padding: 14px;
            }
        }

        /* 超小屏幕优化 */
        @media (max-width: 480px) {
            .xiaomi-card {
                border-radius: 8px;
                padding: 16px !important;
            }

            .xiaomi-button {
                border-radius: 8px;
                padding: 12px 16px;
            }

            .xiaomi-input {
                border-radius: 8px;
                padding: 12px;
            }
        }

        /* 动画效果 */
        @keyframes xiaomi-fade-in {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .xiaomi-fade-in {
            animation: xiaomi-fade-in 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes xiaomi-pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .xiaomi-pulse {
            animation: xiaomi-pulse 2s infinite;
        }

        /* 加载动画 */
        .xiaomi-loader {
            width: 24px;
            height: 24px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>

    <link rel="stylesheet" href="/css/styles.css">
</head>
<body class="bg-xiaomi-light font-xiaomi min-h-screen">
    <div class="w-full mx-auto px-4 sm:px-6 lg:px-8">
        <!-- 顶部导航栏 -->
        <nav class="bg-white shadow-xiaomi sticky top-0 z-50 mb-8 rounded-xiaomi-lg">
            <div class="px-4 sm:px-6 py-4">
                <div class="flex justify-between items-center">
                    <!-- Logo和标题 -->
                    <div class="flex items-center space-x-4">
                        <div class="w-8 h-8 xiaomi-gradient rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"/>
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-xiaomi-text">AI图片评估助手</h1>
                            <p class="text-sm text-gray-500">专业多视角图片评估 · 夸夸机正能量模式</p>
                        </div>
                    </div>

                    <!-- 用户信息和操作 -->
                    <div class="flex items-center space-x-4">
                        <a href="/gallery.html" class="bg-blue-100 hover:bg-blue-200 text-blue-700 px-4 py-2 rounded-xiaomi text-sm transition-colors font-medium">
                            🎨 公开画廊
                        </a>
                        <div id="user-info" class="hidden flex items-center space-x-3">
                            <span class="text-sm text-gray-600">欢迎，</span>
                            <span id="username" class="text-sm font-semibold text-xiaomi-text"></span>
                            <button id="logout-btn" class="text-sm text-red-500 hover:text-red-700 transition-colors">退出</button>
                        </div>
                        <button id="login-btn" class="xiaomi-button px-6 py-2 text-sm">
                            登录
                        </button>
                        <button id="admin-btn" class="hidden bg-gray-100 hover:bg-gray-200 text-xiaomi-text px-4 py-2 rounded-xiaomi text-sm transition-colors">
                            管理
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <div class="xiaomi-card p-4 sm:p-6 lg:p-8 mb-6 md:mb-8 xiaomi-fade-in">

            <div id="disclaimer" class="bg-blue-50 border border-blue-200 text-blue-800 rounded-lg p-4 md:p-6 text-sm md:text-base relative" role="alert">
                <strong class="font-bold">💡 使用说明:</strong>
                <span class="block sm:inline">这是一个AI图片评估工具，可以从多种专业视角分析你的图片。夸夸机模式专门给你正能量，其他视角提供专业建议。评估结果仅供参考和娱乐，请理性对待！</span>
                <span class="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer" onclick="document.getElementById('disclaimer').style.display='none'">
                    <svg class="fill-current h-6 w-6 text-blue-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                </span>
            </div>

            <!-- API 配置区域 -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <p class="text-gray-700 font-medium text-lg">🔧 API 配置</p>
                    <button id="toggle-config" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        <span id="toggle-text">展开配置</span>
                        <svg id="toggle-icon" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block ml-1 transform transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                </div>

                <div id="config-panel" class="hidden">
                    <!-- API 预设选择器 -->
                    <div class="mb-4">
                        <p class="form-label mb-3">选择 API 配置:</p>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4 mb-4">
                            <div class="bg-blue-600 border-2 border-blue-600 text-white rounded-lg p-4 cursor-pointer transition-all duration-200 text-center hover:bg-blue-100 hover:border-blue-400 selected" data-preset="preset1">
                                <div class="font-semibold text-base mb-1">配置一 (默认)</div>
                                <div class="text-sm opacity-80">zone.veloera.org</div>
                                <div class="text-sm opacity-80">gpt-4.1-mini</div>
                            </div>
                            <div class="bg-blue-50 border-2 border-slate-300 rounded-lg p-4 cursor-pointer transition-all duration-200 text-center hover:bg-blue-100 hover:border-blue-400" data-preset="preset2">
                                <div class="font-semibold text-gray-900 text-base mb-1">配置二</div>
                                <div class="text-gray-600 text-sm opacity-80">tbai.xin</div>
                                <div class="text-gray-600 text-sm opacity-80">gemini-1.5-flash-8b</div>
                            </div>
                        </div>
                        <div class="bg-blue-50 border-2 border-slate-300 rounded-lg p-4 cursor-pointer transition-all duration-200 text-center hover:bg-blue-100 hover:border-blue-400" data-preset="custom">
                            <div class="font-semibold text-gray-900 text-base mb-1">自定义配置</div>
                            <div class="text-gray-600 text-sm opacity-80">使用自己的 API 设置</div>
                        </div>
                    </div>

                    <!-- 自定义配置区域 -->
                    <div id="custom-config-section" class="border-t border-gray-200 pt-4 mt-4 hidden">
                        <div class="grid grid-cols-1 gap-4">
                            <div class="mb-4">
                                <label for="api-base-url" class="block text-sm font-medium text-gray-900 mb-2">Base URL</label>
                                <input type="text" id="api-base-url" class="w-full px-3 py-2 border border-slate-300 rounded-lg text-sm transition-all duration-200 focus:outline-none focus:border-blue-600 focus:ring focus:ring-blue-200" placeholder="https://api.openai.com">
                            </div>
                            <div class="mb-4">
                                <label for="api-key" class="block text-sm font-medium text-gray-900 mb-2">API Key</label>
                                <input type="password" id="api-key" class="w-full px-3 py-2 border border-slate-300 rounded-lg text-sm transition-all duration-200 focus:outline-none focus:border-blue-600 focus:ring focus:ring-blue-200" placeholder="sk-...">
                            </div>
                            <div class="mb-4">
                                <label for="api-model" class="block text-sm font-medium text-gray-900 mb-2">模型</label>
                                <input type="text" id="api-model" class="w-full px-3 py-2 border border-slate-300 rounded-lg text-sm transition-all duration-200 focus:outline-none focus:border-blue-600 focus:ring focus:ring-blue-200" placeholder="gpt-4">
                            </div>
                        </div>
                    </div>

                    <div class="flex flex-col sm:flex-row justify-end gap-3 sm:gap-4 mt-4">
                        <button id="reset-config" class="bg-blue-50 text-gray-600 font-semibold rounded-lg border border-slate-300 transition-all duration-200 hover:bg-blue-100 hover:border-blue-600 px-4 py-2 text-sm w-full sm:w-auto">重置默认</button>
                        <button id="save-config" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-all duration-200 px-4 py-2 text-sm w-full sm:w-auto">保存配置</button>
                    </div>
                </div>
            </div>

            <!-- AI 评估配置区域 -->
            <div class="mb-6">
                <!-- 评估风格选择 -->
                <div class="option-group mb-6">
                    <h3 class="text-gray-700 font-semibold text-lg mb-4 flex items-center gap-2">
                        <span class="w-1 h-6 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full"></span>
                        🎭 选择评估风格
                    </h3>
                    <div id="style-options" class="option-buttons grid grid-cols-1 md:grid-cols-3 gap-3">
                        <!-- 动态加载评估风格选项 -->
                    </div>
                </div>

                <!-- 专业评估视角选择 -->
                <div class="option-group mb-6">
                    <h3 class="text-gray-700 font-semibold text-lg mb-4 flex items-center gap-2">
                        <span class="w-1 h-6 bg-gradient-to-b from-purple-500 to-pink-600 rounded-full"></span>
                        👁️ 选择专业评估视角
                    </h3>
                    <div id="perspective-options" class="option-buttons grid grid-cols-2 md:grid-cols-4 gap-3">
                        <!-- 动态加载评估视角选项 -->
                    </div>
                </div>

                <!-- 分析详细程度 -->
                <div class="option-group mb-6">
                    <h3 class="text-gray-700 font-semibold text-lg mb-4 flex items-center gap-2">
                        <span class="w-1 h-6 bg-gradient-to-b from-green-500 to-blue-600 rounded-full"></span>
                        📝 选择分析详细程度
                    </h3>
                    <div id="detail-options" class="option-buttons grid grid-cols-1 md:grid-cols-3 gap-3">
                        <!-- 动态加载详细程度选项 -->
                    </div>
                </div>
            </div>

            <!-- 图片上传区域 -->
            <div class="flex flex-col items-center space-y-4 mb-6">
                <div id="upload-area" class="w-full">
                    <!-- 上传方式选择 -->
                    <div class="flex gap-4 mb-4 justify-center">
                        <button id="upload-file-btn" class="xiaomi-button flex items-center gap-2 px-6 py-3 flex-1">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            选择文件
                        </button>
                        <button id="camera-btn" class="bg-xiaomi-blue hover:bg-blue-600 text-white flex items-center gap-2 px-6 py-3 rounded-xiaomi transition-all flex-1">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            拍照上传
                        </button>
                    </div>

                    <!-- 上传区域（无图片时显示） -->
                    <label for="image-upload" id="upload-zone" class="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center cursor-pointer text-slate-500 transition-all duration-300 bg-white hover:border-blue-600 hover:text-blue-600 hover:bg-blue-50 w-full block">
                        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 mb-3 transition-colors duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                        </svg>
                        <p id="upload-text" class="text-sm font-medium mb-2">拖拽图片到这里或<span class="text-blue-600 font-semibold cursor-pointer">点击上传多张图片</span></p>
                        <p class="text-xs text-gray-400">支持 Ctrl+V 粘贴图片，支持多选</p>
                        <input id="image-upload" type="file" accept="image/*" multiple class="input-file">
                    </label>

                    <!-- 拍照区域 -->
                    <div id="camera-area" class="mt-4 hidden">
                        <div class="bg-gray-100 rounded-xl p-4">
                            <video id="camera-video" autoplay class="w-full max-w-md mx-auto rounded-lg"></video>
                            <canvas id="camera-canvas" class="hidden"></canvas>
                            <div class="flex justify-center gap-4 mt-4">
                                <button id="take-photo-btn" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                    📷 拍照
                                </button>
                                <button id="close-camera-btn" class="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                                    关闭摄像头
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 图片预览区域（上传后显示） -->
                    <div id="image-preview-container" class="hidden bg-slate-50 border border-gray-200 rounded-lg p-4 w-full">
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-sm font-medium text-gray-700">已上传图片 (<span id="image-count">0</span><span class="text-gray-600">/5张图片</span>)</span>
                            <div class="flex gap-2">
                                <button id="add-more-images-btn" class="text-green-600 hover:text-green-800 text-sm font-medium flex items-center space-x-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                    </svg>
                                    <span>添加更多</span>
                                </button>
                                <button id="clear-all-images-btn" class="text-red-600 hover:text-red-800 text-sm font-medium flex items-center space-x-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                    <span>清空全部</span>
                                </button>
                            </div>
                        </div>
                        <div id="images-grid" class="grid grid-cols-2 md:grid-cols-3 gap-4">
                            <!-- 动态生成的图片预览 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 评估按钮 -->
            <div class="flex justify-center mb-6">
                <button id="evaluate-button" class="xiaomi-button w-full md:w-auto px-12 py-4 text-lg font-bold xiaomi-pulse flex items-center justify-center space-x-2">
                    <span id="button-text">开始AI评估</span>
                    <div id="loader" class="xiaomi-loader hidden"></div>
                </button>
            </div>

            <!-- 结果显示区域 -->
            <div id="results-container" class="xiaomi-card p-6 md:p-8 mt-6 md:mt-8 hidden">
                <h2 class="text-2xl font-bold text-xiaomi-text mb-6 flex items-center">
                    <div class="w-8 h-8 xiaomi-gradient rounded-lg mr-3 flex items-center justify-center">
                        <span class="text-white">🎯</span>
                    </div>
                    评估结果
                </h2>

                <div class="bg-xiaomi-gray rounded-xiaomi-lg p-6 mb-6">
                    <div class="flex items-center space-x-4 mb-4">
                        <span id="verdict" class="text-4xl font-bold text-xiaomi-text"></span>
                        <div class="text-right">
                            <span id="rating" class="text-2xl font-bold text-xiaomi-orange"></span>
                            <span class="text-lg text-gray-500">/100</span>
                            <div id="rating-emoji" class="text-2xl"></div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xiaomi p-4">
                        <p id="explanation" class="text-xiaomi-text leading-relaxed whitespace-pre-wrap"></p>
                    </div>
                </div>
                <!-- 保存选项 -->
                <div id="save-options" class="mb-4 hidden">
                    <label class="flex items-center space-x-2 text-sm text-gray-600">
                        <input type="checkbox" id="public-checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                        <span>设为公开（其他用户可以看到）</span>
                    </label>
                </div>

                <!-- 操作按钮 -->
                <div class="flex flex-col sm:flex-row gap-4">
                    <button id="try-again-button" class="bg-gray-100 hover:bg-gray-200 text-xiaomi-text px-6 py-3 rounded-xiaomi transition-colors flex-1 flex items-center justify-center space-x-2">
                        <span>🔄</span>
                        <span>再试一次</span>
                    </button>
                    <button id="save-button" class="xiaomi-button px-6 py-3 flex-1 flex items-center justify-center space-x-2">
                        <span>💾</span>
                        <span>保存结果</span>
                    </button>
                    <button id="share-button" class="bg-xiaomi-blue hover:bg-blue-600 text-white px-6 py-3 rounded-xiaomi transition-colors flex-1 flex items-center justify-center space-x-2">
                        <span>📤</span>
                        <span>分享结果</span>
                    </button>
                </div>
            </div>

            <!-- 错误信息 -->
            <div id="error-message" class="bg-red-50 text-red-800 border border-red-400 rounded-lg p-4 md:p-6 text-sm md:text-base hidden">
                <p class="font-bold">发生错误:</p>
                <p id="error-text"></p>
            </div>

            <!-- 评估结果区域 -->
            <div id="evaluations-section" class="mt-8 md:mt-10 pt-6 md:pt-8 border-t border-gray-200">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">评估结果</h2>
                    <div class="flex space-x-2">
                        <button id="public-tab" class="px-4 py-2 text-sm font-medium rounded-lg bg-blue-600 text-white">
                            公开评估
                        </button>
                        <button id="personal-tab" class="px-4 py-2 text-sm font-medium rounded-lg bg-gray-200 text-gray-700 hover:bg-gray-300 hidden">
                            我的评估
                        </button>
                    </div>
                </div>

                <!-- 公开评估列表 -->
                <div id="public-evaluations" class="space-y-4">
                    <div id="public-evaluations-list" class="max-h-[600px] overflow-y-auto pr-1 md:pr-2 space-y-4"></div>
                    <div class="text-center">
                        <button id="load-more-public" class="bg-blue-50 text-blue-600 px-6 py-2 rounded-lg hover:bg-blue-100 text-sm font-medium hidden">
                            加载更多
                        </button>
                        <div id="public-loading" class="hidden">
                            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                        </div>
                    </div>
                </div>

                <!-- 个人评估列表 -->
                <div id="personal-evaluations" class="space-y-4 hidden">
                    <!-- 批量操作控制栏 -->
                    <div id="batch-controls" class="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4 hidden">
                        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
                            <div class="flex items-center space-x-4">
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" id="select-all-checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                                    <span class="text-sm text-gray-700">全选</span>
                                </label>
                                <span id="selected-count" class="text-sm text-gray-600">已选择 0 项</span>
                            </div>
                            <div class="flex space-x-2">
                                <button id="batch-share-btn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed" disabled>
                                    批量分享
                                </button>
                                <button id="batch-delete-btn" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed" disabled>
                                    批量删除
                                </button>
                                <button id="cancel-batch-btn" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                    取消选择
                                </button>
                            </div>
                        </div>
                    </div>

                    <div id="personal-evaluations-list" class="max-h-[600px] overflow-y-auto pr-1 md:pr-2 space-y-4"></div>
                    <div class="text-center">
                        <button id="load-more-personal" class="bg-blue-50 text-blue-600 px-6 py-2 rounded-lg hover:bg-blue-100 text-sm font-medium hidden">
                            加载更多
                        </button>
                        <div id="personal-loading" class="hidden">
                            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分享模态框 -->
        <div id="share-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 share-modal" style="display: none;">
            <div class="bg-white rounded-xl shadow-2xl p-6 md:p-8 max-w-lg w-full max-h-[90vh] overflow-y-auto share-modal-content">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold text-gray-900">分享评估结果</h3>
                    <button id="close-share-modal" class="text-gray-500 hover:text-gray-700">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <div class="space-y-4">
                    <!-- 分享链接 -->
                    <div class="flex items-center p-3 md:p-4 border border-gray-200 rounded-lg cursor-pointer transition-all duration-200 hover:bg-blue-50 hover:border-blue-600 mb-3" id="share-link-option">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                        </svg>
                        <div>
                            <div class="font-semibold text-gray-900">复制分享链接</div>
                            <div class="text-sm text-gray-600">生成链接，其他人可以查看评估结果</div>
                        </div>
                    </div>

                    <!-- 下载图片 -->
                    <div class="flex items-center p-3 md:p-4 border border-gray-200 rounded-lg cursor-pointer transition-all duration-200 hover:bg-blue-50 hover:border-blue-600 mb-3" id="share-image-option">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        <div>
                            <div class="font-semibold text-gray-900">下载分享图片</div>
                            <div class="text-sm text-gray-600">生成包含评估结果的图片</div>
                        </div>
                    </div>

                    <!-- 社交媒体分享 -->
                    <div class="border-t pt-4 mt-4">
                        <h4 class="font-semibold text-gray-900 mb-3">分享到社交平台</h4>
                        <div class="grid grid-cols-2 gap-3">
                            <div class="flex items-center p-3 md:p-4 border border-gray-200 rounded-lg cursor-pointer transition-all duration-200 hover:bg-blue-50 hover:border-blue-600 mb-3" id="share-weibo-option">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-500 mr-3 flex-shrink-0" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M9.31 8.17c-.36.06-.61.39-.55.75.06.36.39.61.75.55 1.99-.33 3.7.48 4.31 2.06.14.35.53.52.88.38.35-.14.52-.53.38-.88-.89-2.3-3.39-3.69-5.77-2.86z"/>
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2.54 15.5c-3.23 0-5.85-2.09-5.85-4.67 0-2.58 2.62-4.67 5.85-4.67s5.85 2.09 5.85 4.67c0 2.58-2.62 4.67-5.85 4.67z"/>
                                </svg>
                                <span class="text-sm">微博</span>
                            </div>
                            <div class="flex items-center p-3 md:p-4 border border-gray-200 rounded-lg cursor-pointer transition-all duration-200 hover:bg-blue-50 hover:border-blue-600 mb-3" id="share-twitter-option">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-400 mr-3 flex-shrink-0" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                </svg>
                                <span class="text-sm">Twitter</span>
                            </div>
                        </div>
                    </div>

                    <!-- 分享预览 -->
                    <div class="bg-slate-50 border border-gray-200 rounded-lg p-4 my-4 text-sm text-gray-600 hidden" id="share-preview">
                        <div class="font-semibold mb-2">分享链接预览：</div>
                        <div id="share-url" class="break-all bg-white p-2 rounded border text-xs"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 认证模态框 -->
        <div id="auth-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 auth-modal hidden">
            <div class="bg-white rounded-xl shadow-2xl p-6 md:p-8 max-w-md w-full auth-modal-content">
                <div class="flex justify-between items-center mb-6">
                    <h3 id="auth-modal-title" class="text-xl font-bold text-gray-900">登录</h3>
                    <button id="close-auth-modal" class="text-gray-500 hover:text-gray-700">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <form id="auth-form" class="space-y-4">
                    <div id="username-field">
                        <label for="auth-username" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" id="auth-username" name="username" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                    </div>

                    <div id="email-field" class="hidden">
                        <label for="auth-email" class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
                        <input type="email" id="auth-email" name="email" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div id="password-field">
                        <label for="auth-password" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input type="password" id="auth-password" name="password" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                    </div>

                    <button type="submit" id="auth-submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 rounded-lg transition-colors">
                        登录
                    </button>
                </form>

                <div class="mt-4 text-center">
                    <p class="text-sm text-gray-600">
                        <span id="auth-switch-text">还没有账户？</span>
                        <button id="auth-switch" class="text-blue-600 hover:text-blue-800 font-medium">注册</button>
                    </p>
                    <p id="forgot-password-link" class="text-sm text-gray-600 mt-2">
                        <button id="forgot-password" class="text-blue-600 hover:text-blue-800 font-medium">忘记密码？</button>
                    </p>
                </div>

                <div id="auth-error" class="mt-4 text-red-600 text-sm hidden"></div>
                <div id="auth-success" class="mt-4 text-green-600 text-sm hidden"></div>
            </div>
        </div>

        <!-- 图片查看器 -->
        <div id="image-viewer" class="image-viewer">
            <button class="close-btn" onclick="closeImageViewer()">×</button>
            <img id="viewer-image" src="" alt="查看图片">
        </div>

        <!-- 项目信息和感谢 -->
        <div class="bg-white rounded-xl shadow-lg p-6 md:p-10 border border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
            <div class="text-center space-y-4">
                <h3 class="text-xl font-bold text-blue-800 mb-4">🤖 AI图片评估助手</h3>
                <div class="space-y-3 text-gray-700">
                    <div class="bg-white rounded-lg p-4 mb-4">
                        <h4 class="font-semibold text-gray-800 mb-2">✨ 核心功能</h4>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded">🌟 夸夸机模式</span>
                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded">📸 摄影师视角</span>
                            <span class="bg-orange-100 text-orange-800 px-2 py-1 rounded">🍕 美食家视角</span>
                            <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded">🎨 艺术家视角</span>
                            <span class="bg-emerald-100 text-emerald-800 px-2 py-1 rounded">🌿 自然爱好者</span>
                            <span class="bg-rose-100 text-rose-800 px-2 py-1 rounded">✈️ 旅行博主</span>
                            <span class="bg-pink-100 text-pink-800 px-2 py-1 rounded">👗 时尚达人</span>
                            <span class="bg-indigo-100 text-indigo-800 px-2 py-1 rounded">💡 传统视角</span>
                        </div>
                    </div>

                    <p class="flex items-center justify-center space-x-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                        </svg>
                        <span>在线体验：</span>
                        <a href="https://sexup.deno.dev/" target="_blank" class="text-blue-600 hover:text-blue-800 font-semibold underline">https://sexup.deno.dev/</a>
                    </p>
                    <p class="flex items-center justify-center space-x-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>服务状态：</span>
                        <a href="/health" target="_blank" class="text-green-600 hover:text-green-800 font-semibold underline">/health</a>
                    </p>
                </div>

                <div class="border-t border-blue-200 pt-4 mt-6">
                    <div class="space-y-2 text-sm text-gray-600">
                        <p class="flex items-center justify-center space-x-2">
                            <span class="text-purple-500">⚔️</span>
                            <span><strong class="text-blue-700">十年磨一剑</strong> 自豪的采用 <strong class="text-blue-700">Deno</strong> 和 <strong class="text-blue-700">CockroachLabs</strong> 驱动</span>
                        </p>
                        <p class="flex items-center justify-center space-x-2">
                            <span class="text-blue-500">©</span>
                            <span><strong class="text-blue-700">@2025</strong></span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/js/api-client.js"></script>
    <script src="/js/evaluation-config.js"></script>
    <script src="/js/app.js" type="module"></script>
</body>
</html>
