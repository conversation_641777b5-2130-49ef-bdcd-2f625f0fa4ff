<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理面板 - SexUp AI评估系统</title>
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- 小米风格字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- 小米风格配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'xiaomi-orange': '#FF6900',
                        'xiaomi-blue': '#0084FF',
                        'xiaomi-gray': '#F5F5F5',
                        'xiaomi-dark': '#1A1A1A',
                        'xiaomi-text': '#333333',
                        'xiaomi-light': '#FAFAFA'
                    },
                    fontFamily: {
                        'xiaomi': ['Inter', 'system-ui', '-apple-system', 'sans-serif']
                    },
                    borderRadius: {
                        'xiaomi': '12px',
                        'xiaomi-lg': '16px',
                        'xiaomi-xl': '20px'
                    },
                    boxShadow: {
                        'xiaomi': '0 2px 20px rgba(0, 0, 0, 0.08)',
                        'xiaomi-lg': '0 8px 40px rgba(0, 0, 0, 0.12)',
                        'xiaomi-hover': '0 4px 30px rgba(0, 0, 0, 0.15)'
                    }
                }
            }
        }
    </script>

    <style>
        /* 小米风格自定义样式 */
        .xiaomi-gradient {
            background: linear-gradient(135deg, #FF6900 0%, #FF8F00 100%);
        }

        .xiaomi-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .xiaomi-card:hover {
            box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
        }

        .xiaomi-button {
            background: linear-gradient(135deg, #FF6900 0%, #FF8F00 100%);
            border: none;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 20px rgba(255, 105, 0, 0.3);
        }

        .xiaomi-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 30px rgba(255, 105, 0, 0.4);
        }

        .xiaomi-tab {
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .xiaomi-tab.active {
            background: linear-gradient(135deg, #FF6900 0%, #FF8F00 100%);
            color: white;
            box-shadow: 0 4px 20px rgba(255, 105, 0, 0.3);
        }

        .xiaomi-tab:not(.active) {
            background: #F5F5F5;
            color: #666666;
        }

        .xiaomi-tab:not(.active):hover {
            background: #FFF5F0;
            color: #FF6900;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .tab-button.active {
            background: linear-gradient(135deg, #FF6900 0%, #FF8F00 100%);
            color: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(255, 105, 0, 0.3);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .xiaomi-card {
                border-radius: 12px;
            }

            .xiaomi-tab {
                padding: 10px 16px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body class="bg-xiaomi-light font-xiaomi">
    <div class="min-h-screen">
        <!-- 导航栏 -->
        <nav class="bg-white shadow-xiaomi sticky top-0 z-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <!-- Logo和标题 -->
                    <div class="flex items-center space-x-4">
                        <div class="w-8 h-8 xiaomi-gradient rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h1 class="text-xl font-bold text-xiaomi-text">管理面板</h1>
                    </div>

                    <!-- 用户信息和操作 -->
                    <div class="flex items-center space-x-4">
                        <span id="user-info" class="text-sm text-gray-600"></span>
                        <a href="/" class="bg-gray-100 hover:bg-gray-200 text-xiaomi-text px-4 py-2 rounded-xiaomi text-sm transition-colors">返回首页</a>
                        <button id="logout-btn" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-xiaomi text-sm transition-colors">
                            退出登录
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <!-- 标签页导航 -->
            <div class="mb-6">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8">
                        <button class="tab-button active py-2 px-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="users">
                            用户管理
                        </button>
                        <button class="tab-button py-2 px-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="roles">
                            角色管理
                        </button>
                        <button class="tab-button py-2 px-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="permissions">
                            权限管理
                        </button>
                        <button class="tab-button py-2 px-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="evaluations">
                            评估记录
                        </button>
                        <button class="tab-button py-2 px-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="shares">
                            分享管理
                        </button>
                        <button class="tab-button py-2 px-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="api">
                            API状态
                        </button>
                    </nav>
                </div>
            </div>

            <!-- 用户管理 -->
            <div id="users-tab" class="tab-content active">
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">用户管理</h3>
                        
                        <div class="mb-4 flex justify-between items-center">
                            <input type="text" id="user-search" placeholder="搜索用户..." 
                                   class="border border-gray-300 rounded-md px-3 py-2 w-64">
                            <button id="refresh-users" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                                刷新
                            </button>
                        </div>
                        
                        <div id="users-list" class="space-y-4">
                            <!-- 用户列表将在这里动态加载 -->
                        </div>
                        
                        <div id="users-pagination" class="mt-4 flex justify-center">
                            <!-- 分页控件 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 角色管理 -->
            <div id="roles-tab" class="tab-content">
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">角色管理</h3>
                        
                        <div class="mb-4">
                            <button id="create-role-btn" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                                创建角色
                            </button>
                        </div>
                        
                        <div id="roles-list" class="space-y-4">
                            <!-- 角色列表将在这里动态加载 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 权限管理 -->
            <div id="permissions-tab" class="tab-content">
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">权限管理</h3>
                        
                        <div class="mb-4">
                            <select id="permission-resource-filter" class="border border-gray-300 rounded-md px-3 py-2 mr-4">
                                <option value="">所有资源</option>
                            </select>
                            <button id="create-permission-btn" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                                创建权限
                            </button>
                        </div>
                        
                        <div id="permissions-list" class="space-y-4">
                            <!-- 权限列表将在这里动态加载 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 评估记录 -->
            <div id="evaluations-tab" class="tab-content">
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">评估记录</h3>
                        
                        <div id="evaluations-list" class="space-y-4">
                            <!-- 评估记录将在这里动态加载 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分享管理 -->
            <div id="shares-tab" class="tab-content">
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">分享管理</h3>
                        
                        <div class="mb-4">
                            <button id="refresh-shares" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                                刷新公开分享
                            </button>
                        </div>
                        
                        <div id="shares-list" class="space-y-4">
                            <!-- 分享列表将在这里动态加载 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- API状态 -->
            <div id="api-tab" class="tab-content">
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">API状态</h3>
                        
                        <div id="api-status" class="space-y-4">
                            <!-- API状态信息将在这里显示 -->
                        </div>
                        
                        <div class="mt-6">
                            <h4 class="text-md font-medium text-gray-900 mb-2">API文档</h4>
                            <div id="api-docs" class="bg-gray-50 p-4 rounded">
                                <!-- API文档将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 错误提示 -->
    <div id="error-message" class="fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded shadow-lg hidden">
        <span id="error-text"></span>
        <button onclick="hideError()" class="ml-2 text-white hover:text-gray-200">&times;</button>
    </div>

    <!-- 成功提示 -->
    <div id="success-message" class="fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded shadow-lg hidden">
        <span id="success-text"></span>
        <button onclick="hideSuccess()" class="ml-2 text-white hover:text-gray-200">&times;</button>
    </div>

    <!-- 加载指示器 -->
    <div id="loading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden">
        <div class="bg-white p-4 rounded-lg">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
            <p class="mt-2 text-gray-600">加载中...</p>
        </div>
    </div>

    <script src="/js/api-client.js"></script>
    <script src="/js/admin.js"></script>
</body>
</html>
