/**
 * 评估配置管理
 * 动态加载和管理评估选项
 */

class EvaluationConfig {
  constructor() {
    this.styles = []
    this.perspectives = []
    this.details = []
    this.selectedStyle = 'CIVIL'
    this.selectedPerspective = 'MALE'
    this.selectedDetail = 'SMALL'
  }

  // 初始化配置
  async initialize() {
    try {
      await this.loadConfigs()
      this.renderUI()
      this.setupEvents()
    } catch (error) {
      console.error('初始化评估配置失败:', error)
      // 使用默认配置
      this.useDefaultConfigs()
      this.renderUI()
      this.setupEvents()
    }
  }

  // 加载配置数据
  async loadConfigs() {
    try {
      if (window.apiClient) {
        const data = await window.apiClient.getEvaluationOptions()
        this.styles = data.styles || []
        this.perspectives = data.perspectives || []
        this.details = data.details || []
      } else {
        // 如果API客户端不可用，使用fetch调用v1 API
        const response = await fetch('/api/v1/configs/evaluation-options')
        if (response.ok) {
          const data = await response.json()
          this.styles = data.styles || []
          this.perspectives = data.perspectives || []
          this.details = data.details || []
        } else {
          throw new Error('v1 API请求失败')
        }
      }
    } catch (error) {
      console.error('加载配置失败，使用默认配置:', error)
      this.useDefaultConfigs()
    }
  }

  // 使用默认配置
  useDefaultConfigs() {
    this.styles = [
      { code: 'CIVIL', name: '文明版本', description: '温和客观，适合大众', icon: '🎭', color: 'blue' },
      { code: 'PRAISE', name: '夸夸机', description: '正能量满满，积极鼓励', icon: '🌟', color: 'yellow' },
      { code: 'VULGAR', name: '直白版本', description: '直接不加修饰，真实表达', icon: '💥', color: 'red' }
    ]

    this.perspectives = [
      { code: 'MALE', name: '男性视角', description: '从传统男性审美角度', icon: '👨', color: 'blue' },
      { code: 'FEMALE', name: '女性视角', description: '从女性审美角度', icon: '👩', color: 'pink' },
      { code: 'PHOTOGRAPHER', name: '摄影师', description: '专业摄影技术角度', icon: '📸', color: 'purple' },
      { code: 'ARTIST', name: '艺术家', description: '艺术美学价值角度', icon: '🎨', color: 'indigo' },
      { code: 'FOODIE', name: '美食家', description: '美食视觉呈现角度', icon: '🍽️', color: 'orange' },
      { code: 'NATURE', name: '自然爱好者', description: '生态和自然角度', icon: '🌿', color: 'green' },
      { code: 'TRAVEL', name: '旅行博主', description: '旅行和风景角度', icon: '✈️', color: 'cyan' },
      { code: 'FASHION', name: '时尚达人', description: '时尚穿搭角度', icon: '👗', color: 'rose' }
    ]

    this.details = [
      { code: 'SMALL', name: '简洁', description: '简短评价（1-2句话）', icon: '📝' },
      { code: 'MIDDEN', name: '详细', description: '中等详细分析（3-5句话）', icon: '📄' },
      { code: 'LARGE', name: '深度', description: '全面深度评估（8+句话）', icon: '📚' }
    ]
  }

  // 渲染UI
  renderUI() {
    this.renderStyles()
    this.renderPerspectives()
    this.renderDetails()
    this.updateSelectedDisplays()
  }

  // 更新选中项显示
  updateSelectedDisplays() {
    // 更新风格显示
    const styleOption = this.getOptionDetails('style', this.selectedStyle)
    if (styleOption) {
      this.updateSelectedDisplay('style', styleOption.name)
    }

    // 更新视角显示
    const perspectiveOption = this.getOptionDetails('perspective', this.selectedPerspective)
    if (perspectiveOption) {
      this.updateSelectedDisplay('perspective', perspectiveOption.name)
    }

    // 更新详细程度显示
    const detailOption = this.getOptionDetails('detail', this.selectedDetail)
    if (detailOption) {
      this.updateSelectedDisplay('detail', detailOption.name)
    }
  }

  // 更新单个选中项显示
  updateSelectedDisplay(type, optionName) {
    const selectedElement = document.getElementById(`${type}-selected`)
    if (selectedElement) {
      selectedElement.textContent = optionName
    }
  }

  // 渲染评估风格
  renderStyles() {
    const container = document.getElementById('style-options')
    if (!container) return

    container.innerHTML = this.styles.map(style => `
      <button id="style-${style.code.toLowerCase()}"
              class="group relative bg-white border-2 rounded-xl p-4 cursor-pointer transition-all duration-300 text-left min-h-[100px] flex flex-col justify-center hover:shadow-lg hover:-translate-y-1 ${this.selectedStyle === style.code ? 'border-blue-500 bg-gradient-to-br from-blue-500 to-purple-600 text-white shadow-lg -translate-y-1' : 'border-gray-200 hover:border-blue-300'}"
              data-value="${style.code}"
              data-type="style">
        <div class="flex items-center gap-3">
          <div class="text-2xl ${this.selectedStyle === style.code ? 'filter brightness-125' : ''}">
            ${style.icon}
          </div>
          <div class="flex-1">
            <span class="block font-semibold text-base mb-1 ${this.selectedStyle === style.code ? 'text-white' : 'text-gray-900'}">${style.name}</span>
            <span class="block text-sm ${this.selectedStyle === style.code ? 'text-white/90' : 'text-gray-600'}">${style.description}</span>
          </div>
          <div class="text-lg text-green-500 transition-all duration-300 ${this.selectedStyle === style.code ? 'opacity-100 scale-100 text-white' : 'opacity-0 scale-50'}">✓</div>
        </div>
      </button>
    `).join('')
  }

  // 渲染评估视角
  renderPerspectives() {
    const container = document.getElementById('perspective-options')
    if (!container) return

    container.innerHTML = this.perspectives.map(perspective => `
      <button id="perspective-${perspective.code.toLowerCase()}"
              class="group relative bg-white border-2 rounded-lg p-3 cursor-pointer transition-all duration-300 text-left min-h-[80px] flex flex-col justify-center hover:shadow-md hover:-translate-y-0.5 ${this.selectedPerspective === perspective.code ? 'border-purple-500 bg-gradient-to-br from-purple-500 to-pink-600 text-white shadow-md -translate-y-0.5' : 'border-gray-200 hover:border-purple-300'}"
              data-value="${perspective.code}"
              data-type="perspective">
        <div class="flex items-center gap-2">
          <div class="text-lg ${this.selectedPerspective === perspective.code ? 'filter brightness-125' : ''}">
            ${perspective.icon}
          </div>
          <div class="flex-1">
            <span class="block font-semibold text-sm mb-0.5 ${this.selectedPerspective === perspective.code ? 'text-white' : 'text-gray-900'}">${perspective.name}</span>
            <span class="block text-xs ${this.selectedPerspective === perspective.code ? 'text-white/90' : 'text-gray-600'}">${perspective.description}</span>
          </div>
          <div class="text-sm text-green-500 transition-all duration-300 ${this.selectedPerspective === perspective.code ? 'opacity-100 scale-100 text-white' : 'opacity-0 scale-50'}">✓</div>
        </div>
      </button>
    `).join('')
  }

  // 渲染详细程度
  renderDetails() {
    const container = document.getElementById('detail-options')
    if (!container) return

    container.innerHTML = this.details.map(detail => `
      <button id="detail-${detail.code.toLowerCase()}"
              class="group relative bg-white border-2 rounded-xl p-4 cursor-pointer transition-all duration-300 text-left min-h-[100px] flex flex-col justify-center hover:shadow-lg hover:-translate-y-1 ${this.selectedDetail === detail.code ? 'border-green-500 bg-gradient-to-br from-green-500 to-blue-600 text-white shadow-lg -translate-y-1' : 'border-gray-200 hover:border-green-300'}"
              data-value="${detail.code}"
              data-type="detail">
        <div class="flex items-center gap-3">
          <div class="text-2xl ${this.selectedDetail === detail.code ? 'filter brightness-125' : ''}">
            ${detail.icon}
          </div>
          <div class="flex-1">
            <span class="block font-semibold text-base mb-1 ${this.selectedDetail === detail.code ? 'text-white' : 'text-gray-900'}">${detail.name}</span>
            <span class="block text-sm ${this.selectedDetail === detail.code ? 'text-white/90' : 'text-gray-600'}">${detail.description}</span>
          </div>
          <div class="text-lg text-green-500 transition-all duration-300 ${this.selectedDetail === detail.code ? 'opacity-100 scale-100 text-white' : 'opacity-0 scale-50'}">✓</div>
        </div>
      </button>
    `).join('')
  }

  // 设置事件监听
  setupEvents() {
    // 使用事件委托，监听容器的点击事件
    const containers = ['style-options', 'perspective-options', 'detail-options']

    containers.forEach(containerId => {
      const container = document.getElementById(containerId)
      if (container) {
        container.addEventListener('click', (e) => {
          // 查找具有data-type属性的按钮元素
          const option = e.target.closest('[data-type]')
          if (!option) return

          const type = option.dataset.type
          const value = option.dataset.value

          if (type && value) {
            this.selectOption(type, value)
          }
        })
      }
    })
  }

  // 选择选项
  selectOption(type, value) {
    // 更新选中状态
    if (type === 'style') {
      this.selectedStyle = value
      // 更新全局变量
      window.currentSelectedStyle = value
    } else if (type === 'perspective') {
      this.selectedPerspective = value
      window.currentSelectedPerspective = value
    } else if (type === 'detail') {
      this.selectedDetail = value
      window.currentSelectedDetail = value
    }

    // 更新UI显示
    this.updateSelectionUI(type, value)

    // 更新选中项显示
    const optionDetails = this.getOptionDetails(type, value)
    if (optionDetails) {
      this.updateSelectedDisplay(type, optionDetails.name)
    }

    // 自动折叠该区域
    this.collapseSection(type)

    // 触发自定义事件
    document.dispatchEvent(new CustomEvent('evaluationConfigChanged', {
      detail: {
        type,
        value,
        config: this.getCurrentConfig()
      }
    }))
  }

  // 折叠指定区域
  collapseSection(type) {
    const content = document.getElementById(`${type}-content`)
    const arrow = document.getElementById(`${type}-arrow`)

    if (content && arrow) {
      content.classList.add('hidden')
      arrow.style.transform = 'rotate(0deg)'
    }
  }

  // 更新选择UI
  updateSelectionUI(type, value) {
    // 重新渲染对应类型的选项
    if (type === 'style') {
      this.renderStyles()
    } else if (type === 'perspective') {
      this.renderPerspectives()
    } else if (type === 'detail') {
      this.renderDetails()
    }
  }

  // 获取当前配置
  getCurrentConfig() {
    return {
      style: this.selectedStyle,
      perspective: this.selectedPerspective,
      detail: this.selectedDetail
    }
  }

  // 获取选项详情
  getOptionDetails(type, code) {
    let options = []
    if (type === 'style') options = this.styles
    else if (type === 'perspective') options = this.perspectives
    else if (type === 'detail') options = this.details

    return options.find(option => option.code === code)
  }

  // 获取当前配置的详细信息
  getCurrentConfigDetails() {
    return {
      style: this.getOptionDetails('style', this.selectedStyle),
      perspective: this.getOptionDetails('perspective', this.selectedPerspective),
      detail: this.getOptionDetails('detail', this.selectedDetail)
    }
  }
}

// 创建全局实例
window.evaluationConfig = new EvaluationConfig()

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EvaluationConfig
}
