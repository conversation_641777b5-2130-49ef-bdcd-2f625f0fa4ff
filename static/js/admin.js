/**
 * 管理面板JavaScript
 * 使用RESTful API和RBAC权限系统
 */

let currentUser = null;

// 初始化管理面板
async function initializeAdmin() {
  try {
    console.log('开始初始化管理面板...');

    // 检查本地是否有token
    const token = localStorage.getItem('authToken');
    console.log('本地token:', token ? '存在' : '不存在');

    if (!token) {
      console.log('没有找到认证token，跳转到登录页');
      alert('请先登录');
      window.location.href = '/';
      return;
    }

    // 设置token到API客户端
    apiClient.setToken(token);

    // 检查用户登录状态和权限
    console.log('检查API状态...');
    const status = await apiClient.getStatus();
    console.log('API状态响应:', status);

    if (!status.authenticated) {
      console.log('用户未认证，清除token并跳转');
      localStorage.removeItem('authToken');
      alert('登录已过期，请重新登录');
      window.location.href = '/';
      return;
    }

    currentUser = status.user;
    console.log('当前用户:', currentUser);
    document.getElementById('user-info').textContent = `欢迎，${currentUser.username}`;

    // 初始化标签页
    setupTabs();

    // 加载初始数据
    await loadUsers();
    await loadApiStatus();

    console.log('管理面板初始化完成');

  } catch (error) {
    console.error('初始化失败:', error);

    // 如果是认证错误，清除token并跳转
    if (error.message.includes('401') || error.message.includes('未登录') || error.message.includes('权限')) {
      console.log('认证错误，清除token并跳转');
      localStorage.removeItem('authToken');
      alert('登录已过期，请重新登录');
      window.location.href = '/';
      return;
    }

    showError('初始化失败: ' + error.message);
  }
}

// 设置标签页
function setupTabs() {
  const tabButtons = document.querySelectorAll('.tab-button');
  const tabContents = document.querySelectorAll('.tab-content');

  tabButtons.forEach(button => {
    button.addEventListener('click', () => {
      const tabName = button.dataset.tab;

      // 更新按钮状态
      tabButtons.forEach(btn => btn.classList.remove('active'));
      button.classList.add('active');

      // 更新内容显示
      tabContents.forEach(content => content.classList.remove('active'));
      document.getElementById(`${tabName}-tab`).classList.add('active');

      // 加载对应数据
      loadTabData(tabName);
    });
  });
}

// 加载标签页数据
async function loadTabData(tabName) {
  try {
    showLoading();

    switch (tabName) {
      case 'users':
        await loadUsers();
        break;
      case 'roles':
        await loadRoles();
        break;
      case 'permissions':
        await loadPermissions();
        break;
      case 'evaluations':
        await loadEvaluations();
        break;
      case 'shares':
        await loadShares();
        break;
      case 'api':
        await loadApiStatus();
        break;
    }
  } catch (error) {
    console.error(`加载${tabName}数据失败:`, error);
    showError(`加载数据失败: ${error.message}`);
  } finally {
    hideLoading();
  }
}

// 加载用户列表
async function loadUsers(page = 1, search = '') {
  try {
    const result = await apiClient.getUsers(page, 20, search);
    const usersList = document.getElementById('users-list');

    usersList.innerHTML = result.users.map(user => `
      <div class="border border-gray-200 rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition-shadow">
        <div class="flex items-start space-x-4">
          <!-- 批量选择复选框 -->
          <div class="user-checkbox-container hidden">
            <input type="checkbox" class="user-checkbox w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500" data-user-id="${user.id}">
          </div>

          <!-- 用户头像 -->
          <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
            ${user.username.charAt(0).toUpperCase()}
          </div>

          <!-- 用户信息 -->
          <div class="flex-1 min-w-0">
            <div class="flex items-center space-x-2 mb-1">
              <h4 class="font-semibold text-lg text-gray-900">${user.username}</h4>
              <span class="px-2 py-1 text-xs rounded-full ${user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                ${user.is_active ? '活跃' : '禁用'}
              </span>
              ${user.default_role_display_name ? `
                <span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                  ${user.default_role_display_name}
                </span>
              ` : ''}
            </div>

            <p class="text-gray-600 mb-2">${user.email || '未设置邮箱'}</p>

            <!-- 统计信息 -->
            <div class="flex items-center space-x-4 text-sm text-gray-500 mb-2">
              <span class="flex items-center">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                ${user.evaluation_count || 0} 评估
              </span>
              <span class="flex items-center">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
                </svg>
                ${user.share_count || 0} 分享
              </span>
            </div>

            <!-- 时间信息 -->
            <div class="text-xs text-gray-400">
              <div>注册: ${new Date(user.created_at).toLocaleString()}</div>
              ${user.last_login_at ? `<div>最后登录: ${new Date(user.last_login_at).toLocaleString()}</div>` : '<div>从未登录</div>'}
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex flex-col space-y-2">
            <button onclick="editUser(${user.id})"
                    class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600 transition-colors">
              编辑
            </button>
            <button onclick="manageUserRoles(${user.id})"
                    class="bg-purple-500 text-white px-3 py-1 rounded text-sm hover:bg-purple-600 transition-colors">
              角色
            </button>
            <button onclick="deleteUser(${user.id})"
                    class="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600 transition-colors">
              删除
            </button>
          </div>
        </div>
      </div>
    `).join('');

    // 更新分页信息
    updateUserPagination(result.pagination);

  } catch (error) {
    console.error('加载用户列表失败:', error);
    showError('加载用户列表失败');
  }
}

// 更新用户分页
function updateUserPagination(pagination) {
  const paginationDiv = document.getElementById('users-pagination');
  if (!paginationDiv) return;

  const { page, totalPages, total } = pagination;

  let paginationHtml = `
    <div class="flex items-center justify-between">
      <div class="text-sm text-gray-700">
        共 ${total} 个用户，第 ${page} / ${totalPages} 页
      </div>
      <div class="flex space-x-2">
  `;

  // 上一页按钮
  if (page > 1) {
    paginationHtml += `
      <button onclick="loadUsersPage(${page - 1})" class="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600">
        上一页
      </button>
    `;
  }

  // 页码按钮
  const startPage = Math.max(1, page - 2);
  const endPage = Math.min(totalPages, page + 2);

  for (let i = startPage; i <= endPage; i++) {
    const isActive = i === page;
    paginationHtml += `
      <button onclick="loadUsersPage(${i})" class="px-3 py-1 ${isActive ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'} rounded">
        ${i}
      </button>
    `;
  }

  // 下一页按钮
  if (page < totalPages) {
    paginationHtml += `
      <button onclick="loadUsersPage(${page + 1})" class="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600">
        下一页
      </button>
    `;
  }

  paginationHtml += `
      </div>
    </div>
  `;

  paginationDiv.innerHTML = paginationHtml;
}

// 加载指定页的用户
function loadUsersPage(page) {
  const searchInput = document.getElementById('user-search');
  const search = searchInput ? searchInput.value.trim() : '';
  loadUsers(page, search);
}

// 加载角色列表
async function loadRoles() {
  try {
    const result = await apiClient.getRoles(1, 50, true);
    const rolesList = document.getElementById('roles-list');

    rolesList.innerHTML = result.roles.map(role => `
      <div class="border border-gray-200 rounded-lg p-4">
        <div class="flex justify-between items-start">
          <div>
            <h4 class="font-medium text-gray-900">${role.display_name}</h4>
            <p class="text-sm text-gray-600">${role.name}</p>
            <p class="text-sm text-gray-500">${role.description || '无描述'}</p>
            <p class="text-sm text-gray-500">
              用户数: ${role.user_count} | 
              ${role.is_system ? '系统角色' : '自定义角色'}
            </p>
          </div>
          <div class="flex space-x-2">
            <button onclick="viewRolePermissions(${role.id})" 
                    class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
              查看权限
            </button>
            ${!role.is_system ? `
              <button onclick="editRole(${role.id})" 
                      class="bg-yellow-500 text-white px-3 py-1 rounded text-sm hover:bg-yellow-600">
                编辑
              </button>
              <button onclick="deleteRole(${role.id})" 
                      class="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600">
                删除
              </button>
            ` : ''}
          </div>
        </div>
      </div>
    `).join('');

  } catch (error) {
    console.error('加载角色列表失败:', error);
    showError('加载角色列表失败');
  }
}

// 加载权限列表
async function loadPermissions() {
  try {
    const result = await apiClient.getPermissions(1, 100);
    const permissionsList = document.getElementById('permissions-list');

    // 按资源分组显示权限
    const groupedPermissions = {};
    result.permissions.forEach(perm => {
      if (!groupedPermissions[perm.resource]) {
        groupedPermissions[perm.resource] = [];
      }
      groupedPermissions[perm.resource].push(perm);
    });

    permissionsList.innerHTML = Object.entries(groupedPermissions).map(([resource, permissions]) => `
      <div class="border border-gray-200 rounded-lg p-4">
        <h4 class="font-medium text-gray-900 mb-2">${resource} 资源权限</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
          ${permissions.map(perm => `
            <div class="bg-gray-50 p-2 rounded text-sm">
              <div class="font-medium">${perm.display_name}</div>
              <div class="text-gray-600">${perm.name}</div>
              <div class="text-gray-500">角色数: ${perm.role_count}</div>
            </div>
          `).join('')}
        </div>
      </div>
    `).join('');

    // 更新资源筛选器
    const resourceFilter = document.getElementById('permission-resource-filter');
    resourceFilter.innerHTML = '<option value="">所有资源</option>' +
      result.resources.map(resource => `<option value="${resource}">${resource}</option>`).join('');

  } catch (error) {
    console.error('加载权限列表失败:', error);
    showError('加载权限列表失败');
  }
}

// 加载评估记录
async function loadEvaluations() {
  try {
    const result = await apiClient.getEvaluations(1, 20);
    const evaluationsList = document.getElementById('evaluations-list');

    evaluationsList.innerHTML = result.evaluations.map(evaluation => `
      <div class="border border-gray-200 rounded-lg p-4">
        <div class="flex justify-between items-start">
          <div>
            <h4 class="font-medium text-gray-900">${evaluation.title}</h4>
            <p class="text-sm text-gray-600">创建者: ${evaluation.creator_username}</p>
            <p class="text-sm text-gray-500">
              ${evaluation.style} 风格 | ${evaluation.perspective} 视角 | ${evaluation.detail} 详细度
            </p>
            <p class="text-sm text-gray-500">
              评分: ${evaluation.rating} | 结论: ${evaluation.verdict}
            </p>
            <p class="text-sm text-gray-500">
              创建时间: ${new Date(evaluation.created_at).toLocaleString()}
            </p>
          </div>
          <div class="flex space-x-2">
            <button onclick="viewEvaluation(${evaluation.id})" 
                    class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
              查看详情
            </button>
          </div>
        </div>
      </div>
    `).join('');

  } catch (error) {
    console.error('加载评估记录失败:', error);
    showError('加载评估记录失败');
  }
}

// 加载分享列表
async function loadShares() {
  try {
    const result = await apiClient.getPublicShares(1, 20);
    const sharesList = document.getElementById('shares-list');

    sharesList.innerHTML = result.shares.map(share => `
      <div class="border border-gray-200 rounded-lg p-4">
        <div class="flex justify-between items-start">
          <div>
            <h4 class="font-medium text-gray-900">分享 ${share.id}</h4>
            <p class="text-sm text-gray-600">创建者: ${share.creatorUsername || '匿名'}</p>
            <p class="text-sm text-gray-500">
              查看次数: ${share.viewCount} | 
              创建时间: ${new Date(share.createdAt).toLocaleString()}
            </p>
          </div>
          <div class="flex space-x-2">
            <button onclick="viewShare('${share.id}')" 
                    class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
              查看内容
            </button>
            <a href="${share.shareUrl}" target="_blank"
               class="bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600 inline-block">
              访问链接
            </a>
          </div>
        </div>
      </div>
    `).join('');

  } catch (error) {
    console.error('加载分享列表失败:', error);
    showError('加载分享列表失败');
  }
}

// 加载API状态
async function loadApiStatus() {
  try {
    const [status, docs] = await Promise.all([
      apiClient.getStatus(),
      apiClient.getDocs()
    ]);

    const apiStatusDiv = document.getElementById('api-status');
    apiStatusDiv.innerHTML = `
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="bg-green-50 p-4 rounded">
          <h4 class="font-medium text-green-900">API状态</h4>
          <p class="text-green-700">状态: ${status.status}</p>
          <p class="text-green-700">版本: ${status.version}</p>
          <p class="text-green-700">时间: ${new Date(status.timestamp).toLocaleString()}</p>
        </div>
        <div class="bg-blue-50 p-4 rounded">
          <h4 class="font-medium text-blue-900">用户信息</h4>
          <p class="text-blue-700">已认证: ${status.authenticated ? '是' : '否'}</p>
          ${status.user ? `
            <p class="text-blue-700">用户: ${status.user.username}</p>
            <p class="text-blue-700">ID: ${status.user.id}</p>
          ` : ''}
        </div>
      </div>
    `;

    const apiDocsDiv = document.getElementById('api-docs');
    apiDocsDiv.innerHTML = `
      <h5 class="font-medium mb-2">${docs.title}</h5>
      <p class="text-sm text-gray-600 mb-4">${docs.description}</p>
      <div class="text-sm">
        <p><strong>版本:</strong> ${docs.version}</p>
        <p><strong>认证:</strong> ${docs.authentication.type}</p>
        <p><strong>权限系统:</strong> ${docs.permissions.description}</p>
      </div>
    `;

  } catch (error) {
    console.error('加载API状态失败:', error);
    showError('加载API状态失败');
  }
}

// 工具函数
function showError(message) {
  const errorDiv = document.getElementById('error-message');
  const errorText = document.getElementById('error-text');
  errorText.textContent = message;
  errorDiv.classList.remove('hidden');
  setTimeout(() => hideError(), 5000);
}

function hideError() {
  document.getElementById('error-message').classList.add('hidden');
}

function showSuccess(message) {
  const successDiv = document.getElementById('success-message');
  const successText = document.getElementById('success-text');
  successText.textContent = message;
  successDiv.classList.remove('hidden');
  setTimeout(() => hideSuccess(), 3000);
}

function hideSuccess() {
  document.getElementById('success-message').classList.add('hidden');
}

function showLoading() {
  document.getElementById('loading').classList.remove('hidden');
}

function hideLoading() {
  document.getElementById('loading').classList.add('hidden');
}

// 事件处理函数（占位符）
function viewUserRoles(userId) {
  alert(`查看用户 ${userId} 的角色`);
}

function editUser(userId) {
  showEditUserModal(userId);
}

function manageUserRoles(userId) {
  showUserRolesModal(userId);
}

function deleteUser(userId) {
  if (confirm('确定要删除这个用户吗？此操作不可恢复！')) {
    deleteUserById(userId);
  }
}

function viewRolePermissions(roleId) {
  alert(`查看角色 ${roleId} 的权限`);
}

function editRole(roleId) {
  alert(`编辑角色 ${roleId}`);
}

function deleteRole(roleId) {
  if (confirm('确定要删除这个角色吗？')) {
    alert(`删除角色 ${roleId}`);
  }
}

function viewEvaluation(evaluationId) {
  alert(`查看评估 ${evaluationId}`);
}

function viewShare(shareId) {
  alert(`查看分享 ${shareId}`);
}

// 退出登录
document.getElementById('logout-btn').addEventListener('click', () => {
  if (confirm('确定要退出登录吗？')) {
    apiClient.setToken(null);
    window.location.href = '/';
  }
});

// 用户编辑模态框
function showEditUserModal(userId) {
  // 创建模态框HTML
  const modalHtml = `
    <div id="edit-user-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div class="bg-white rounded-lg max-w-md w-full p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold">编辑用户</h3>
          <button onclick="closeEditUserModal()" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <form id="edit-user-form">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
              <input type="text" id="edit-username" class="w-full border border-gray-300 rounded-md px-3 py-2" readonly>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
              <input type="email" id="edit-email" class="w-full border border-gray-300 rounded-md px-3 py-2">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
              <select id="edit-status" class="w-full border border-gray-300 rounded-md px-3 py-2">
                <option value="true">活跃</option>
                <option value="false">禁用</option>
              </select>
            </div>
          </div>

          <div class="flex justify-end space-x-3 mt-6">
            <button type="button" onclick="closeEditUserModal()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
              取消
            </button>
            <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
              保存
            </button>
          </div>
        </form>
      </div>
    </div>
  `;

  document.body.insertAdjacentHTML('beforeend', modalHtml);

  // 加载用户数据
  loadUserForEdit(userId);
}

function closeEditUserModal() {
  const modal = document.getElementById('edit-user-modal');
  if (modal) {
    modal.remove();
  }
}

async function loadUserForEdit(userId) {
  try {
    const user = await apiClient.getUser(userId);
    document.getElementById('edit-username').value = user.user.username;
    document.getElementById('edit-email').value = user.user.email || '';
    document.getElementById('edit-status').value = user.user.is_active.toString();

    // 绑定表单提交事件
    document.getElementById('edit-user-form').addEventListener('submit', async (e) => {
      e.preventDefault();
      await updateUser(userId);
    });
  } catch (error) {
    showError('加载用户信息失败: ' + error.message);
    closeEditUserModal();
  }
}

async function updateUser(userId) {
  try {
    const email = document.getElementById('edit-email').value;
    const isActive = document.getElementById('edit-status').value === 'true';

    await apiClient.updateUser(userId, {
      email: email,
      isActive: isActive
    });

    showSuccess('用户信息更新成功');
    closeEditUserModal();
    await loadUsers(); // 重新加载用户列表
  } catch (error) {
    showError('更新用户失败: ' + error.message);
  }
}

// 删除用户功能
async function deleteUserById(userId) {
  try {
    await apiClient.deleteUser(userId);
    showSuccess('用户删除成功');
    await loadUsers(); // 重新加载用户列表
  } catch (error) {
    showError('删除用户失败: ' + error.message);
  }
}

// 用户角色管理模态框
function showUserRolesModal(userId) {
  // 创建模态框HTML
  const modalHtml = `
    <div id="user-roles-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div class="bg-white rounded-lg max-w-lg w-full p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold">管理用户角色</h3>
          <button onclick="closeUserRolesModal()" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div id="user-roles-content">
          <div class="text-center py-4">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
            <p class="mt-2 text-gray-500">加载中...</p>
          </div>
        </div>
      </div>
    </div>
  `;

  document.body.insertAdjacentHTML('beforeend', modalHtml);

  // 加载用户角色数据
  loadUserRoles(userId);
}

function closeUserRolesModal() {
  const modal = document.getElementById('user-roles-modal');
  if (modal) {
    modal.remove();
  }
}

async function loadUserRoles(userId) {
  try {
    const [userRoles, allRoles] = await Promise.all([
      apiClient.getUserRoles(userId),
      apiClient.getRoles(1, 100)
    ]);

    const userRoleIds = userRoles.roles.map(role => role.id);

    const content = `
      <div class="space-y-4">
        <div>
          <h4 class="font-medium text-gray-900 mb-2">当前角色</h4>
          <div class="space-y-2">
            ${userRoles.roles.map(role => `
              <div class="flex items-center justify-between p-2 bg-blue-50 rounded">
                <span class="text-blue-800">${role.display_name}</span>
                <button onclick="removeUserRole(${userId}, ${role.id})" class="text-red-600 hover:text-red-800 text-sm">
                  移除
                </button>
              </div>
            `).join('')}
          </div>
        </div>

        <div>
          <h4 class="font-medium text-gray-900 mb-2">可分配角色</h4>
          <div class="space-y-2 max-h-40 overflow-y-auto">
            ${allRoles.roles.filter(role => !userRoleIds.includes(role.id)).map(role => `
              <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span class="text-gray-800">${role.display_name}</span>
                <button onclick="assignUserRole(${userId}, ${role.id})" class="text-green-600 hover:text-green-800 text-sm">
                  分配
                </button>
              </div>
            `).join('')}
          </div>
        </div>
      </div>
    `;

    document.getElementById('user-roles-content').innerHTML = content;
  } catch (error) {
    document.getElementById('user-roles-content').innerHTML = `
      <div class="text-center py-4 text-red-600">
        加载失败: ${error.message}
      </div>
    `;
  }
}

async function assignUserRole(userId, roleId) {
  try {
    await apiClient.assignUserRole(userId, roleId);
    showSuccess('角色分配成功');
    await loadUserRoles(userId); // 重新加载角色列表
    await loadUsers(); // 重新加载用户列表
  } catch (error) {
    showError('分配角色失败: ' + error.message);
  }
}

async function removeUserRole(userId, roleId) {
  try {
    await apiClient.removeUserRole(userId, roleId);
    showSuccess('角色移除成功');
    await loadUserRoles(userId); // 重新加载角色列表
    await loadUsers(); // 重新加载用户列表
  } catch (error) {
    showError('移除角色失败: ' + error.message);
  }
}

// 批量选择功能
let batchSelectMode = false;

function toggleBatchSelectMode() {
  batchSelectMode = !batchSelectMode;
  const checkboxContainers = document.querySelectorAll('.user-checkbox-container');
  const batchControls = document.getElementById('user-batch-controls');
  const batchSelectBtn = document.getElementById('batch-select-users');

  if (batchSelectMode) {
    checkboxContainers.forEach(container => container.classList.remove('hidden'));
    batchControls.classList.remove('hidden');
    batchSelectBtn.textContent = '退出批量选择';
    batchSelectBtn.classList.remove('bg-purple-500', 'hover:bg-purple-600');
    batchSelectBtn.classList.add('bg-gray-500', 'hover:bg-gray-600');
  } else {
    checkboxContainers.forEach(container => container.classList.add('hidden'));
    batchControls.classList.add('hidden');
    batchSelectBtn.textContent = '批量选择';
    batchSelectBtn.classList.remove('bg-gray-500', 'hover:bg-gray-600');
    batchSelectBtn.classList.add('bg-purple-500', 'hover:bg-purple-600');

    // 清除所有选择
    document.querySelectorAll('.user-checkbox').forEach(cb => cb.checked = false);
    updateUserSelectedCount();
  }
}

function updateUserSelectedCount() {
  const selectedCheckboxes = document.querySelectorAll('.user-checkbox:checked');
  const count = selectedCheckboxes.length;
  const countSpan = document.getElementById('user-selected-count');
  const deleteButton = document.getElementById('batch-delete-users-btn');

  countSpan.textContent = `已选择 ${count} 个用户`;
  deleteButton.disabled = count === 0;
}

async function batchDeleteUsers() {
  const selectedCheckboxes = document.querySelectorAll('.user-checkbox:checked');
  const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.dataset.userId);

  if (selectedIds.length === 0) {
    alert('请先选择要删除的用户');
    return;
  }

  if (!confirm(`确定要删除选中的 ${selectedIds.length} 个用户吗？此操作不可恢复！`)) {
    return;
  }

  try {
    const deleteButton = document.getElementById('batch-delete-users-btn');
    const originalText = deleteButton.textContent;
    deleteButton.textContent = '删除中...';
    deleteButton.disabled = true;

    // 逐个删除用户（因为后端可能没有批量删除API）
    const deletePromises = selectedIds.map(async (userId) => {
      try {
        await apiClient.deleteUser(userId);
        return { userId, success: true };
      } catch (error) {
        console.error(`删除用户 ${userId} 失败:`, error);
        return { userId, success: false, error: error.message };
      }
    });

    const deleteResults = await Promise.all(deletePromises);
    const successResults = deleteResults.filter(r => r.success);
    const failedResults = deleteResults.filter(r => !r.success);

    // 显示结果
    if (successResults.length > 0) {
      showSuccess(`成功删除了 ${successResults.length} 个用户`);
    }

    if (failedResults.length > 0) {
      showError(`${failedResults.length} 个用户删除失败`);
    }

    // 恢复按钮状态
    deleteButton.textContent = originalText;
    deleteButton.disabled = false;

    // 退出批量选择模式并刷新列表
    toggleBatchSelectMode();
    await loadUsers();

  } catch (error) {
    console.error('批量删除失败:', error);
    showError(`批量删除失败: ${error.message}`);

    // 恢复按钮状态
    const deleteButton = document.getElementById('batch-delete-users-btn');
    deleteButton.textContent = '批量删除';
    deleteButton.disabled = false;
  }
}

// 绑定批量操作事件
document.addEventListener('DOMContentLoaded', () => {
  // 搜索功能
  const searchInput = document.getElementById('user-search');
  if (searchInput) {
    let searchTimeout;
    searchInput.addEventListener('input', () => {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        loadUsers(1, searchInput.value.trim());
      }, 500); // 500ms 防抖
    });
  }

  // 批量选择按钮
  const batchSelectBtn = document.getElementById('batch-select-users');
  if (batchSelectBtn) {
    batchSelectBtn.addEventListener('click', toggleBatchSelectMode);
  }

  // 批量删除按钮
  const batchDeleteBtn = document.getElementById('batch-delete-users-btn');
  if (batchDeleteBtn) {
    batchDeleteBtn.addEventListener('click', batchDeleteUsers);
  }

  // 取消批量选择按钮
  const cancelBatchBtn = document.getElementById('cancel-user-batch-btn');
  if (cancelBatchBtn) {
    cancelBatchBtn.addEventListener('click', toggleBatchSelectMode);
  }

  // 监听复选框变化
  document.addEventListener('change', (e) => {
    if (e.target.classList.contains('user-checkbox')) {
      updateUserSelectedCount();
    }
  });
});

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initializeAdmin);
