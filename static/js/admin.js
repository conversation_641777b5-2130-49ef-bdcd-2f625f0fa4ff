/**
 * 管理面板JavaScript
 * 使用RESTful API和RBAC权限系统
 */

let currentUser = null;

// 初始化管理面板
async function initializeAdmin() {
  try {
    console.log('开始初始化管理面板...');

    // 检查本地是否有token
    const token = localStorage.getItem('authToken');
    console.log('本地token:', token ? '存在' : '不存在');

    if (!token) {
      console.log('没有找到认证token，跳转到登录页');
      alert('请先登录');
      window.location.href = '/';
      return;
    }

    // 设置token到API客户端
    apiClient.setToken(token);

    // 检查用户登录状态和权限
    console.log('检查API状态...');
    const status = await apiClient.getStatus();
    console.log('API状态响应:', status);

    if (!status.authenticated) {
      console.log('用户未认证，清除token并跳转');
      localStorage.removeItem('authToken');
      alert('登录已过期，请重新登录');
      window.location.href = '/';
      return;
    }

    currentUser = status.user;
    console.log('当前用户:', currentUser);
    document.getElementById('user-info').textContent = `欢迎，${currentUser.username}`;

    // 初始化标签页
    setupTabs();

    // 加载初始数据
    await loadUsers();
    await loadApiStatus();

    console.log('管理面板初始化完成');

  } catch (error) {
    console.error('初始化失败:', error);

    // 如果是认证错误，清除token并跳转
    if (error.message.includes('401') || error.message.includes('未登录') || error.message.includes('权限')) {
      console.log('认证错误，清除token并跳转');
      localStorage.removeItem('authToken');
      alert('登录已过期，请重新登录');
      window.location.href = '/';
      return;
    }

    showError('初始化失败: ' + error.message);
  }
}

// 设置标签页
function setupTabs() {
  const tabButtons = document.querySelectorAll('.tab-button');
  const tabContents = document.querySelectorAll('.tab-content');

  tabButtons.forEach(button => {
    button.addEventListener('click', () => {
      const tabName = button.dataset.tab;

      // 更新按钮状态
      tabButtons.forEach(btn => btn.classList.remove('active'));
      button.classList.add('active');

      // 更新内容显示
      tabContents.forEach(content => content.classList.remove('active'));
      document.getElementById(`${tabName}-tab`).classList.add('active');

      // 加载对应数据
      loadTabData(tabName);
    });
  });
}

// 加载标签页数据
async function loadTabData(tabName) {
  try {
    showLoading();

    switch (tabName) {
      case 'users':
        await loadUsers();
        break;
      case 'roles':
        await loadRoles();
        break;
      case 'permissions':
        await loadPermissions();
        break;
      case 'evaluations':
        await loadEvaluations();
        break;
      case 'shares':
        await loadShares();
        break;
      case 'api':
        await loadApiStatus();
        break;
    }
  } catch (error) {
    console.error(`加载${tabName}数据失败:`, error);
    showError(`加载数据失败: ${error.message}`);
  } finally {
    hideLoading();
  }
}

// 加载用户列表
async function loadUsers() {
  try {
    const result = await apiClient.getUsers(1, 20);
    const usersList = document.getElementById('users-list');

    usersList.innerHTML = result.users.map(user => `
      <div class="border border-gray-200 rounded-lg p-4">
        <div class="flex justify-between items-start">
          <div>
            <h4 class="font-medium text-gray-900">${user.username}</h4>
            <p class="text-sm text-gray-600">${user.email || '未设置邮箱'}</p>
            <p class="text-sm text-gray-500">
              创建时间: ${new Date(user.created_at).toLocaleString()}
            </p>
            <p class="text-sm ${user.is_active ? 'text-green-600' : 'text-red-600'}">
              状态: ${user.is_active ? '激活' : '禁用'}
            </p>
          </div>
          <div class="flex space-x-2">
            <button onclick="viewUserRoles(${user.id})" 
                    class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
              查看角色
            </button>
            <button onclick="editUser(${user.id})" 
                    class="bg-yellow-500 text-white px-3 py-1 rounded text-sm hover:bg-yellow-600">
              编辑
            </button>
          </div>
        </div>
      </div>
    `).join('');

  } catch (error) {
    console.error('加载用户列表失败:', error);
    showError('加载用户列表失败');
  }
}

// 加载角色列表
async function loadRoles() {
  try {
    const result = await apiClient.getRoles(1, 50, true);
    const rolesList = document.getElementById('roles-list');

    rolesList.innerHTML = result.roles.map(role => `
      <div class="border border-gray-200 rounded-lg p-4">
        <div class="flex justify-between items-start">
          <div>
            <h4 class="font-medium text-gray-900">${role.display_name}</h4>
            <p class="text-sm text-gray-600">${role.name}</p>
            <p class="text-sm text-gray-500">${role.description || '无描述'}</p>
            <p class="text-sm text-gray-500">
              用户数: ${role.user_count} | 
              ${role.is_system ? '系统角色' : '自定义角色'}
            </p>
          </div>
          <div class="flex space-x-2">
            <button onclick="viewRolePermissions(${role.id})" 
                    class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
              查看权限
            </button>
            ${!role.is_system ? `
              <button onclick="editRole(${role.id})" 
                      class="bg-yellow-500 text-white px-3 py-1 rounded text-sm hover:bg-yellow-600">
                编辑
              </button>
              <button onclick="deleteRole(${role.id})" 
                      class="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600">
                删除
              </button>
            ` : ''}
          </div>
        </div>
      </div>
    `).join('');

  } catch (error) {
    console.error('加载角色列表失败:', error);
    showError('加载角色列表失败');
  }
}

// 加载权限列表
async function loadPermissions() {
  try {
    const result = await apiClient.getPermissions(1, 100);
    const permissionsList = document.getElementById('permissions-list');

    // 按资源分组显示权限
    const groupedPermissions = {};
    result.permissions.forEach(perm => {
      if (!groupedPermissions[perm.resource]) {
        groupedPermissions[perm.resource] = [];
      }
      groupedPermissions[perm.resource].push(perm);
    });

    permissionsList.innerHTML = Object.entries(groupedPermissions).map(([resource, permissions]) => `
      <div class="border border-gray-200 rounded-lg p-4">
        <h4 class="font-medium text-gray-900 mb-2">${resource} 资源权限</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
          ${permissions.map(perm => `
            <div class="bg-gray-50 p-2 rounded text-sm">
              <div class="font-medium">${perm.display_name}</div>
              <div class="text-gray-600">${perm.name}</div>
              <div class="text-gray-500">角色数: ${perm.role_count}</div>
            </div>
          `).join('')}
        </div>
      </div>
    `).join('');

    // 更新资源筛选器
    const resourceFilter = document.getElementById('permission-resource-filter');
    resourceFilter.innerHTML = '<option value="">所有资源</option>' +
      result.resources.map(resource => `<option value="${resource}">${resource}</option>`).join('');

  } catch (error) {
    console.error('加载权限列表失败:', error);
    showError('加载权限列表失败');
  }
}

// 加载评估记录
async function loadEvaluations() {
  try {
    const result = await apiClient.getEvaluations(1, 20);
    const evaluationsList = document.getElementById('evaluations-list');

    evaluationsList.innerHTML = result.evaluations.map(evaluation => `
      <div class="border border-gray-200 rounded-lg p-4">
        <div class="flex justify-between items-start">
          <div>
            <h4 class="font-medium text-gray-900">${evaluation.title}</h4>
            <p class="text-sm text-gray-600">创建者: ${evaluation.creator_username}</p>
            <p class="text-sm text-gray-500">
              ${evaluation.style} 风格 | ${evaluation.perspective} 视角 | ${evaluation.detail} 详细度
            </p>
            <p class="text-sm text-gray-500">
              评分: ${evaluation.rating} | 结论: ${evaluation.verdict}
            </p>
            <p class="text-sm text-gray-500">
              创建时间: ${new Date(evaluation.created_at).toLocaleString()}
            </p>
          </div>
          <div class="flex space-x-2">
            <button onclick="viewEvaluation(${evaluation.id})" 
                    class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
              查看详情
            </button>
          </div>
        </div>
      </div>
    `).join('');

  } catch (error) {
    console.error('加载评估记录失败:', error);
    showError('加载评估记录失败');
  }
}

// 加载分享列表
async function loadShares() {
  try {
    const result = await apiClient.getPublicShares(1, 20);
    const sharesList = document.getElementById('shares-list');

    sharesList.innerHTML = result.shares.map(share => `
      <div class="border border-gray-200 rounded-lg p-4">
        <div class="flex justify-between items-start">
          <div>
            <h4 class="font-medium text-gray-900">分享 ${share.id}</h4>
            <p class="text-sm text-gray-600">创建者: ${share.creatorUsername || '匿名'}</p>
            <p class="text-sm text-gray-500">
              查看次数: ${share.viewCount} | 
              创建时间: ${new Date(share.createdAt).toLocaleString()}
            </p>
          </div>
          <div class="flex space-x-2">
            <button onclick="viewShare('${share.id}')" 
                    class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
              查看内容
            </button>
            <a href="${share.shareUrl}" target="_blank"
               class="bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600 inline-block">
              访问链接
            </a>
          </div>
        </div>
      </div>
    `).join('');

  } catch (error) {
    console.error('加载分享列表失败:', error);
    showError('加载分享列表失败');
  }
}

// 加载API状态
async function loadApiStatus() {
  try {
    const [status, docs] = await Promise.all([
      apiClient.getStatus(),
      apiClient.getDocs()
    ]);

    const apiStatusDiv = document.getElementById('api-status');
    apiStatusDiv.innerHTML = `
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="bg-green-50 p-4 rounded">
          <h4 class="font-medium text-green-900">API状态</h4>
          <p class="text-green-700">状态: ${status.status}</p>
          <p class="text-green-700">版本: ${status.version}</p>
          <p class="text-green-700">时间: ${new Date(status.timestamp).toLocaleString()}</p>
        </div>
        <div class="bg-blue-50 p-4 rounded">
          <h4 class="font-medium text-blue-900">用户信息</h4>
          <p class="text-blue-700">已认证: ${status.authenticated ? '是' : '否'}</p>
          ${status.user ? `
            <p class="text-blue-700">用户: ${status.user.username}</p>
            <p class="text-blue-700">ID: ${status.user.id}</p>
          ` : ''}
        </div>
      </div>
    `;

    const apiDocsDiv = document.getElementById('api-docs');
    apiDocsDiv.innerHTML = `
      <h5 class="font-medium mb-2">${docs.title}</h5>
      <p class="text-sm text-gray-600 mb-4">${docs.description}</p>
      <div class="text-sm">
        <p><strong>版本:</strong> ${docs.version}</p>
        <p><strong>认证:</strong> ${docs.authentication.type}</p>
        <p><strong>权限系统:</strong> ${docs.permissions.description}</p>
      </div>
    `;

  } catch (error) {
    console.error('加载API状态失败:', error);
    showError('加载API状态失败');
  }
}

// 工具函数
function showError(message) {
  const errorDiv = document.getElementById('error-message');
  const errorText = document.getElementById('error-text');
  errorText.textContent = message;
  errorDiv.classList.remove('hidden');
  setTimeout(() => hideError(), 5000);
}

function hideError() {
  document.getElementById('error-message').classList.add('hidden');
}

function showSuccess(message) {
  const successDiv = document.getElementById('success-message');
  const successText = document.getElementById('success-text');
  successText.textContent = message;
  successDiv.classList.remove('hidden');
  setTimeout(() => hideSuccess(), 3000);
}

function hideSuccess() {
  document.getElementById('success-message').classList.add('hidden');
}

function showLoading() {
  document.getElementById('loading').classList.remove('hidden');
}

function hideLoading() {
  document.getElementById('loading').classList.add('hidden');
}

// 事件处理函数（占位符）
function viewUserRoles(userId) {
  alert(`查看用户 ${userId} 的角色`);
}

function editUser(userId) {
  alert(`编辑用户 ${userId}`);
}

function viewRolePermissions(roleId) {
  alert(`查看角色 ${roleId} 的权限`);
}

function editRole(roleId) {
  alert(`编辑角色 ${roleId}`);
}

function deleteRole(roleId) {
  if (confirm('确定要删除这个角色吗？')) {
    alert(`删除角色 ${roleId}`);
  }
}

function viewEvaluation(evaluationId) {
  alert(`查看评估 ${evaluationId}`);
}

function viewShare(shareId) {
  alert(`查看分享 ${shareId}`);
}

// 退出登录
document.getElementById('logout-btn').addEventListener('click', () => {
  if (confirm('确定要退出登录吗？')) {
    apiClient.setToken(null);
    window.location.href = '/';
  }
});

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initializeAdmin);
