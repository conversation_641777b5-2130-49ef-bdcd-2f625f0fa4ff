// 公开评估画廊
class GalleryAPI {
  constructor() {
    this.baseUrl = '/api/v1'
  }

  async getPublicShares(page = 1, limit = 10) {
    const params = new URLSearchParams({ page: page.toString(), limit: limit.toString() })
    const response = await fetch(`${this.baseUrl}/shares/public?${params}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
        // 不发送Authorization头，因为公开API不需要认证
      }
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || `请求失败: ${response.status}`)
    }

    return response.json()
  }
}

class Gallery {
  constructor() {
    this.api = new GalleryAPI()
    this.currentPage = 1
    this.hasMore = true
    this.loading = false
    this.shares = []
    
    this.initializeElements()
    this.bindEvents()
    this.loadShares()
  }

  initializeElements() {
    this.loadingEl = document.getElementById('loading')
    this.errorEl = document.getElementById('error')
    this.emptyEl = document.getElementById('empty')
    this.galleryEl = document.getElementById('gallery')
    this.loadMoreContainer = document.getElementById('load-more-container')
    this.loadMoreBtn = document.getElementById('load-more-btn')
    this.retryBtn = document.getElementById('retry-btn')
    this.detailModal = document.getElementById('detail-modal')
    this.modalContent = document.getElementById('modal-content')
    this.closeModal = document.getElementById('close-modal')
  }

  bindEvents() {
    this.loadMoreBtn?.addEventListener('click', () => this.loadMore())
    this.retryBtn?.addEventListener('click', () => this.retry())
    this.closeModal?.addEventListener('click', () => this.hideModal())
    this.detailModal?.addEventListener('click', (e) => {
      if (e.target === this.detailModal) this.hideModal()
    })
    
    // ESC键关闭模态框
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && !this.detailModal.classList.contains('hidden')) {
        this.hideModal()
      }
    })
  }

  showLoading() {
    this.loadingEl.classList.remove('hidden')
    this.errorEl.classList.add('hidden')
    this.emptyEl.classList.add('hidden')
    this.galleryEl.classList.add('hidden')
  }

  showError() {
    this.loadingEl.classList.add('hidden')
    this.errorEl.classList.remove('hidden')
    this.emptyEl.classList.add('hidden')
    this.galleryEl.classList.add('hidden')
  }

  showEmpty() {
    this.loadingEl.classList.add('hidden')
    this.errorEl.classList.add('hidden')
    this.emptyEl.classList.remove('hidden')
    this.galleryEl.classList.add('hidden')
  }

  showGallery() {
    this.loadingEl.classList.add('hidden')
    this.errorEl.classList.add('hidden')
    this.emptyEl.classList.add('hidden')
    this.galleryEl.classList.remove('hidden')
  }

  async loadShares() {
    if (this.loading) return
    
    this.loading = true
    this.showLoading()

    try {
      const response = await this.api.getPublicShares(this.currentPage, 10)
      console.log('公开分享响应:', response)

      if (!response.shares || response.shares.length === 0) {
        if (this.currentPage === 1) {
          this.showEmpty()
        }
        this.hasMore = false
        this.updateLoadMoreButton()
        return
      }

      this.shares.push(...response.shares)
      this.renderShares(response.shares)
      this.showGallery()

      // 更新分页信息
      this.hasMore = response.pagination.page < response.pagination.totalPages
      this.updateLoadMoreButton()

    } catch (error) {
      console.error('加载公开分享失败:', error)
      this.showError()
    } finally {
      this.loading = false
    }
  }

  renderShares(shares) {
    shares.forEach((share, index) => {
      setTimeout(() => {
        const card = this.createShareCard(share)
        this.galleryEl.appendChild(card)
      }, index * 100) // 错开动画时间
    })
  }

  createShareCard(share) {
    const card = document.createElement('div')
    card.className = 'gallery-card xiaomi-card fade-in-up cursor-pointer'
    
    // 解析分享数据
    const data = typeof share.data === 'string' ? JSON.parse(share.data) : share.data
    
    // 处理图片
    let imageHtml = ''
    if (data.image) {
      const imageUrl = data.image.startsWith('http') ? data.image : `data:${data.mimeType || 'image/jpeg'};base64,${data.image}`
      imageHtml = `
        <div class="w-full aspect-square mb-4 overflow-hidden rounded-xiaomi">
          <img src="${imageUrl}" alt="评估图片" class="w-full h-full object-cover hover:scale-105 transition-transform duration-300">
        </div>
      `
    }

    // 评价等级样式
    const verdictClass = {
      '上': 'verdict-excellent',
      '中': 'verdict-good', 
      '下': 'verdict-poor'
    }[data.verdict] || 'verdict-good'

    card.innerHTML = `
      <div class="p-6">
        ${imageHtml}
        
        <!-- 评分和评价 -->
        <div class="flex justify-between items-center mb-4">
          <div class="rating-badge">${data.rating}/100</div>
          <div class="px-3 py-1 rounded-full text-sm font-medium ${verdictClass}">
            ${data.verdict}
          </div>
        </div>

        <!-- 评估说明 -->
        <p class="text-gray-700 text-sm leading-relaxed mb-4 line-clamp-3">
          ${data.explanation}
        </p>

        <!-- 标签 -->
        <div class="flex flex-wrap gap-2 mb-4">
          <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-lg text-xs font-medium">
            ${data.style}
          </span>
          <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded-lg text-xs font-medium">
            ${data.perspective}
          </span>
          <span class="bg-green-100 text-green-800 px-2 py-1 rounded-lg text-xs font-medium">
            ${data.detail}
          </span>
        </div>

        <!-- 底部信息 -->
        <div class="flex justify-between items-center text-xs text-gray-500">
          <span>by ${share.creatorUsername || '匿名用户'}</span>
          <div class="flex items-center space-x-3">
            <span>👁 ${share.viewCount || 0}</span>
            <span>${new Date(share.createdAt).toLocaleDateString()}</span>
          </div>
        </div>
      </div>
    `

    // 点击事件
    card.addEventListener('click', () => this.showDetail(share))

    return card
  }

  showDetail(share) {
    const data = typeof share.data === 'string' ? JSON.parse(share.data) : share.data
    
    // 处理图片
    let imageHtml = ''
    if (data.image) {
      const imageUrl = data.image.startsWith('http') ? data.image : `data:${data.mimeType || 'image/jpeg'};base64,${data.image}`
      imageHtml = `
        <div class="w-full max-w-md mx-auto mb-6">
          <img src="${imageUrl}" alt="评估图片" class="w-full rounded-xiaomi shadow-lg">
        </div>
      `
    }

    // 评价等级样式
    const verdictClass = {
      '上': 'verdict-excellent',
      '中': 'verdict-good', 
      '下': 'verdict-poor'
    }[data.verdict] || 'verdict-good'

    this.modalContent.innerHTML = `
      ${imageHtml}
      
      <div class="text-center mb-6">
        <div class="rating-badge inline-block text-2xl mb-2">${data.rating}/100</div>
        <div class="inline-block ml-4 px-4 py-2 rounded-full font-medium ${verdictClass}">
          ${data.verdict}
        </div>
      </div>

      <div class="bg-gray-50 rounded-xiaomi p-4 mb-6">
        <h4 class="font-semibold text-xiaomi-text mb-2">AI评估说明</h4>
        <p class="text-gray-700 leading-relaxed">${data.explanation}</p>
      </div>

      <div class="grid grid-cols-3 gap-4 mb-6">
        <div class="text-center">
          <div class="bg-blue-100 text-blue-800 px-3 py-2 rounded-lg font-medium">
            ${data.style}
          </div>
          <div class="text-xs text-gray-500 mt-1">评估风格</div>
        </div>
        <div class="text-center">
          <div class="bg-purple-100 text-purple-800 px-3 py-2 rounded-lg font-medium">
            ${data.perspective}
          </div>
          <div class="text-xs text-gray-500 mt-1">评估视角</div>
        </div>
        <div class="text-center">
          <div class="bg-green-100 text-green-800 px-3 py-2 rounded-lg font-medium">
            ${data.detail}
          </div>
          <div class="text-xs text-gray-500 mt-1">详细程度</div>
        </div>
      </div>

      <div class="border-t pt-4 text-center text-sm text-gray-500">
        <div>分享者：${share.creatorUsername || '匿名用户'}</div>
        <div>查看次数：${share.viewCount || 0} | 分享时间：${new Date(share.createdAt).toLocaleString()}</div>
      </div>
    `

    this.detailModal.classList.remove('hidden')
  }

  hideModal() {
    this.detailModal.classList.add('hidden')
  }

  updateLoadMoreButton() {
    if (this.hasMore && this.shares.length > 0) {
      this.loadMoreContainer.classList.remove('hidden')
    } else {
      this.loadMoreContainer.classList.add('hidden')
    }
  }

  async loadMore() {
    if (!this.hasMore || this.loading) return
    
    this.currentPage++
    this.loadMoreBtn.textContent = '加载中...'
    this.loadMoreBtn.disabled = true

    try {
      await this.loadShares()
    } finally {
      this.loadMoreBtn.textContent = '查看更多精彩评估'
      this.loadMoreBtn.disabled = false
    }
  }

  retry() {
    this.currentPage = 1
    this.shares = []
    this.galleryEl.innerHTML = ''
    this.hasMore = true
    this.loadShares()
  }
}

// 初始化画廊
document.addEventListener('DOMContentLoaded', () => {
  new Gallery()
})
