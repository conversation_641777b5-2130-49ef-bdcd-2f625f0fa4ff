/**
 * RESTful API 客户端
 * 支持新的v1 API接口和RBAC权限系统
 */

class ApiClient {
  constructor() {
    this.baseUrl = '/api/v1'
    this.token = localStorage.getItem('authToken')
    console.log('ApiClient初始化，token:', this.token ? '存在' : '不存在')
    console.log('Token内容（前50字符）:', this.token ? this.token.substring(0, 50) + '...' : 'null')
  }

  // 设置认证token
  setToken(token) {
    this.token = token
    if (token) {
      localStorage.setItem('authToken', token)
    } else {
      localStorage.removeItem('authToken')
    }
  }

  // 获取认证头
  getAuthHeaders() {
    const headers = {
      'Content-Type': 'application/json'
    }

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`
    }

    return headers
  }

  // 通用请求方法
  async request(method, endpoint, data = null) {
    const url = `${this.baseUrl}${endpoint}`
    const options = {
      method,
      headers: this.getAuthHeaders()
    }

    if (data) {
      options.body = JSON.stringify(data)
    }

    console.log(`API请求 [${method} ${endpoint}]:`, {
      url,
      headers: options.headers,
      hasData: !!data
    })

    try {
      const response = await fetch(url, options)
      console.log(`API响应 [${method} ${endpoint}]:`, {
        status: response.status,
        ok: response.ok
      })

      const result = await response.json()

      if (!response.ok) {
        console.error(`API错误 [${method} ${endpoint}]:`, result)
        throw new Error(result.error || `HTTP ${response.status}`)
      }

      return result
    } catch (error) {
      console.error(`API请求失败 [${method} ${endpoint}]:`, error)
      throw error
    }
  }

  // GET请求
  async get(endpoint) {
    return this.request('GET', endpoint)
  }

  // POST请求
  async post(endpoint, data) {
    return this.request('POST', endpoint, data)
  }

  // PUT请求
  async put(endpoint, data) {
    return this.request('PUT', endpoint, data)
  }

  // DELETE请求
  async delete(endpoint) {
    return this.request('DELETE', endpoint)
  }

  // 用户相关API
  async getUsers(page = 1, limit = 20, search = '') {
    const params = new URLSearchParams({ page, limit, search })
    return this.get(`/users?${params}`)
  }

  async getUser(userId) {
    return this.get(`/users/${userId}`)
  }

  async updateUser(userId, data) {
    return this.put(`/users/${userId}`, data)
  }

  async deleteUser(userId) {
    return this.delete(`/users/${userId}`)
  }

  async getUserRoles(userId) {
    return this.get(`/users/${userId}/roles`)
  }

  async assignUserRole(userId, roleName, expiresAt = null) {
    return this.post(`/users/${userId}/roles`, { roleName, expiresAt })
  }

  async removeUserRole(userId, roleName) {
    return this.delete(`/users/${userId}/roles/${roleName}`)
  }

  // 评估相关API
  async createEvaluation(data) {
    return this.post('/evaluations', data)
  }

  // 保存已有的评估结果（不进行AI评估）
  async saveEvaluation(data) {
    return this.post('/evaluations/save', data)
  }

  // 上传文件到CDN
  async uploadFile(file) {
    const formData = new FormData()
    formData.append('file', file)

    const response = await fetch(`${this.baseUrl}/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`
      },
      body: formData
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || `上传失败: ${response.status}`)
    }

    return response.json()
  }

  // 批量上传文件到CDN
  async uploadFiles(files) {
    const formData = new FormData()
    files.forEach((file, index) => {
      formData.append(`file${index}`, file)
    })

    const response = await fetch(`${this.baseUrl}/upload/batch`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`
      },
      body: formData
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || `批量上传失败: ${response.status}`)
    }

    return response.json()
  }

  async getEvaluations(page = 1, limit = 20, userId = null) {
    const params = new URLSearchParams({ page, limit })
    if (userId) params.append('userId', userId)
    return this.get(`/evaluations?${params}`)
  }

  async getEvaluation(evaluationId) {
    return this.get(`/evaluations/${evaluationId}`)
  }

  async updateEvaluation(evaluationId, data) {
    return this.put(`/evaluations/${evaluationId}`, data)
  }

  async deleteEvaluation(evaluationId) {
    return this.delete(`/evaluations/${evaluationId}`)
  }

  // 批量删除评估
  async batchDeleteEvaluations(ids) {
    return this.request('DELETE', '/evaluations/batch', { ids })
  }

  // 分享相关API
  async createShare(data) {
    return this.post('/shares', data)
  }

  async getShares(page = 1, limit = 20) {
    const params = new URLSearchParams({ page, limit })
    return this.get(`/shares?${params}`)
  }

  async getShare(shareId) {
    return this.get(`/shares/${shareId}`)
  }

  async updateShare(shareId, data) {
    return this.put(`/shares/${shareId}`, data)
  }

  async deleteShare(shareId) {
    return this.delete(`/shares/${shareId}`)
  }

  async getPublicShares(page = 1, limit = 20) {
    const params = new URLSearchParams({ page, limit })
    // 公开API不需要认证，直接调用fetch
    const response = await fetch(`${this.baseUrl}/shares/public?${params}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
        // 不发送Authorization头
      }
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || `请求失败: ${response.status}`)
    }

    return response.json()
  }

  // 获取公开评估列表（无需认证）
  async getPublicEvaluations(page = 1, limit = 10) {
    const params = new URLSearchParams({ page, limit })
    // 公开API不需要认证，直接调用fetch
    const response = await fetch(`${this.baseUrl}/evaluations/public?${params}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
        // 不发送Authorization头
      }
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || `请求失败: ${response.status}`)
    }

    return response.json()
  }

  // 获取公开评估详情（无需认证）
  async getPublicEvaluationDetail(evaluationId) {
    // 公开API不需要认证，直接调用fetch
    const response = await fetch(`${this.baseUrl}/evaluations/public/${evaluationId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
        // 不发送Authorization头
      }
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || `请求失败: ${response.status}`)
    }

    return response.json()
  }

  // 角色相关API
  async getRoles(page = 1, limit = 50, includeSystem = true) {
    const params = new URLSearchParams({ page, limit, includeSystem })
    return this.get(`/roles?${params}`)
  }

  async getRole(roleId) {
    return this.get(`/roles/${roleId}`)
  }

  async createRole(data) {
    return this.post('/roles', data)
  }

  async updateRole(roleId, data) {
    return this.put(`/roles/${roleId}`, data)
  }

  async deleteRole(roleId) {
    return this.delete(`/roles/${roleId}`)
  }

  async getRolePermissions(roleId) {
    return this.get(`/roles/${roleId}/permissions`)
  }

  async assignRolePermissions(roleId, permissionIds) {
    return this.post(`/roles/${roleId}/permissions`, { permissionIds })
  }

  // 权限相关API
  async getPermissions(page = 1, limit = 50, resource = null) {
    const params = new URLSearchParams({ page, limit })
    if (resource) params.append('resource', resource)
    return this.get(`/permissions?${params}`)
  }

  async getPermission(permissionId) {
    return this.get(`/permissions/${permissionId}`)
  }

  async createPermission(data) {
    return this.post('/permissions', data)
  }

  async updatePermission(permissionId, data) {
    return this.put(`/permissions/${permissionId}`, data)
  }

  async deletePermission(permissionId) {
    return this.delete(`/permissions/${permissionId}`)
  }

  // 配置相关API
  async getEvaluationOptions() {
    return this.get('/configs/evaluation-options')
  }

  async getEvaluationStyles() {
    return this.get('/configs/evaluation-styles')
  }

  async getEvaluationPerspectives() {
    return this.get('/configs/evaluation-perspectives')
  }

  async getEvaluationDetails() {
    return this.get('/configs/evaluation-details')
  }

  async getApiConfigs() {
    return this.get('/configs/api-configs')
  }

  async createApiConfig(data) {
    return this.post('/configs/api-configs', data)
  }

  async updateApiConfig(configId, data) {
    return this.put(`/configs/api-configs/${configId}`, data)
  }

  async deleteApiConfig(configId) {
    return this.delete(`/configs/api-configs/${configId}`)
  }

  // API状态检查
  async getStatus() {
    return this.get('/status')
  }

  // 刷新token
  async refreshToken() {
    try {
      const response = await this.post('/auth/refresh')
      if (response.token) {
        this.setToken(response.token)
        console.log('Token刷新成功')
        return response
      }
      throw new Error('刷新响应中没有token')
    } catch (error) {
      console.error('Token刷新失败:', error)
      // 刷新失败，清除token并跳转到登录页
      this.setToken(null)
      throw error
    }
  }

  // API文档
  async getDocs() {
    return this.get('/docs')
  }


}

// 创建全局API客户端实例
window.apiClient = new ApiClient()

// 导出API客户端类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ApiClient
}
