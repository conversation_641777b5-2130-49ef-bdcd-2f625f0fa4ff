// AI图片评估助手 - 前端应用逻辑

// 默认 API 配置预设
const API_PRESETS = {
  preset1: {
    baseUrl: 'https://zone.veloera.org/v1/chat/completions',
    apiKey: 'sk-wm9Qw1dvOo9O9Wu0oenj45zv4EBNapBA3c04Rd3KIhRNH0eB',
    model: 'gpt-4.1-mini'
  },
  preset2: {
    baseUrl: 'https://tbai.xin/v1/chat/completions',
    apiKey: 'sk-zQHsfN5pWKFIeLO2VYrd25eaxFTbBV9Apr5bqLTVer1gZVpI',
    model: 'gemini-1.5-flash-8b'
  }
};

// 配置存储键
const API_CONFIG_STORAGE_KEY = 'sbs_api_config';
const API_PRESET_STORAGE_KEY = 'sbs_api_preset';

// 获取当前选择的预设
function getCurrentPreset() {
  try {
    return localStorage.getItem(API_PRESET_STORAGE_KEY) || 'preset1';
  } catch (error) {
    return 'preset1';
  }
}

// 保存当前选择的预设
function saveCurrentPreset(preset) {
  localStorage.setItem(API_PRESET_STORAGE_KEY, preset);
}

// 获取当前 API 配置
function getApiConfig() {
  const currentPreset = getCurrentPreset();

  if (currentPreset === 'custom') {
    try {
      const saved = localStorage.getItem(API_CONFIG_STORAGE_KEY);
      return saved ? JSON.parse(saved) : API_PRESETS.preset1;
    } catch (error) {
      return API_PRESETS.preset1;
    }
  } else {
    return API_PRESETS[currentPreset] || API_PRESETS.preset1;
  }
}

// 保存自定义 API 配置
function saveApiConfig(config) {
  localStorage.setItem(API_CONFIG_STORAGE_KEY, JSON.stringify(config));
}

// 全局变量
let uploadedImages = []; // 改为数组支持多图
let currentSelectedStyle = 'CIVIL';       // 文明/夸夸机/粗俗
let currentSelectedPerspective = 'MALE';  // 评估视角
let currentSelectedDetail = 'SMALL';      // 详细程度
let currentEvaluationResult = null;
let cameraStream = null; // 摄像头流
const STORAGE_KEY = 'sbs_saved_results';

// DOM 元素引用
const verdictElem = document.getElementById('verdict');
const ratingElem = document.getElementById('rating');
const ratingEmojiElem = document.getElementById('rating-emoji');
const explanationElem = document.getElementById('explanation');

// 工具函数
function showError(message) {
  const errorDiv = document.getElementById('error-message');
  const errorText = document.getElementById('error-text');
  errorText.textContent = message;
  errorDiv.classList.remove('hidden');

  // 3秒后自动隐藏错误信息
  setTimeout(() => {
    errorDiv.classList.add('hidden');
  }, 3000);
}

function hideError() {
  document.getElementById('error-message').classList.add('hidden');
}

// 显示加载状态
function showLoading() {
  const button = document.getElementById('evaluate-button');
  const buttonText = document.getElementById('button-text');
  const loader = document.getElementById('loader');

  button.disabled = true;
  buttonText.textContent = '评估中...';
  loader.classList.remove('hidden');
}

// 隐藏加载状态
function hideLoading() {
  const button = document.getElementById('evaluate-button');
  const buttonText = document.getElementById('button-text');
  const loader = document.getElementById('loader');

  button.disabled = false;
  buttonText.textContent = '查看评估的结果';
  loader.classList.add('hidden');
}

// 选择按钮处理函数
function selectButton(groupSelector, selectedButton, value, variableName) {
  // 移除所有按钮的选中状态
  document.querySelectorAll(groupSelector).forEach(btn => {
    btn.classList.remove('selected', 'bg-blue-600', 'border-blue-600', 'text-white');
    btn.classList.add('bg-blue-50', 'border-slate-300');
    const spans = btn.querySelectorAll('span:not(.checkmark-icon)');
    spans.forEach(span => {
      span.classList.remove('text-white', 'opacity-80');
      span.classList.add('text-gray-900', 'text-gray-600');
    });
  });

  // 设置选中按钮的样式
  selectedButton.classList.add('selected', 'bg-blue-600', 'border-blue-600', 'text-white');
  selectedButton.classList.remove('bg-blue-50', 'border-slate-300');
  const spans = selectedButton.querySelectorAll('span:not(.checkmark-icon)');
  spans.forEach(span => {
    span.classList.add('text-white');
    span.classList.remove('text-gray-900', 'text-gray-600');
  });

  // 更新全局变量
  window[variableName] = value;
}

// 初始化函数
function initializeApp() {
  console.log('AI图片评估助手初始化中...');

  // 初始化API配置
  initializeApiConfig();

  // 初始化事件监听器
  initializeEventListeners();

  // 初始化历史评估功能
  initializeEvaluationHistory();

  // 默认加载公开评估
  loadDefaultPublicShares();

  console.log('应用初始化完成');
}

// 默认加载公开评估
async function loadDefaultPublicShares() {
  try {
    console.log('开始加载默认公开评估...');

    // 确保公开评估标签是默认选中的
    const publicTab = document.getElementById('public-tab');
    const personalTab = document.getElementById('personal-tab');
    const publicEvaluations = document.getElementById('public-evaluations');
    const personalEvaluations = document.getElementById('personal-evaluations');

    if (publicTab && personalTab && publicEvaluations && personalEvaluations) {
      // 设置公开评估为默认选中
      publicTab.classList.add('bg-blue-600', 'text-white');
      publicTab.classList.remove('bg-gray-200', 'text-gray-700');
      personalTab.classList.remove('bg-blue-600', 'text-white');
      personalTab.classList.add('bg-gray-200', 'text-gray-700');

      // 显示公开评估，隐藏个人评估
      publicEvaluations.classList.remove('hidden');
      personalEvaluations.classList.add('hidden');

      // 加载公开评估数据
      await loadPublicEvaluations();
      console.log('默认公开评估加载完成');
    }
  } catch (error) {
    console.error('加载默认公开评估失败:', error);
    // 失败时不显示错误，静默处理
  }
}

// 初始化API配置
function initializeApiConfig() {
  const currentPreset = getCurrentPreset();

  // 设置预设选择器的初始状态
  document.querySelectorAll('[data-preset]').forEach(elem => {
    elem.classList.remove('selected', 'bg-blue-600', 'border-blue-600', 'text-white');
    elem.classList.add('bg-blue-50', 'border-slate-300');

    if (elem.dataset.preset === currentPreset) {
      elem.classList.add('selected', 'bg-blue-600', 'border-blue-600', 'text-white');
      elem.classList.remove('bg-blue-50', 'border-slate-300');
    }
  });

  // 如果是自定义配置，显示自定义配置区域
  if (currentPreset === 'custom') {
    document.getElementById('custom-config-section').classList.remove('hidden');
    const config = getApiConfig();
    document.getElementById('api-base-url').value = config.baseUrl || '';
    document.getElementById('api-key').value = config.apiKey || '';
    document.getElementById('api-model').value = config.model || '';
  }
}

// 初始化事件监听器
function initializeEventListeners() {
  // API配置相关事件
  setupApiConfigEvents();

  // 评估选项相关事件
  setupEvaluationOptionEvents();

  // 图片上传相关事件
  setupImageUploadEvents();

  // 评估按钮事件
  setupEvaluationButtonEvents();

  // 模态框事件
  setupModalEvents();

  // 认证相关事件
  setupAuthEvents();
}

// 设置API配置相关事件
function setupApiConfigEvents() {
  // 配置面板切换
  document.getElementById('toggle-config').addEventListener('click', function () {
    const panel = document.getElementById('config-panel');
    const icon = document.getElementById('toggle-icon');
    const text = document.getElementById('toggle-text');

    if (panel.classList.contains('hidden')) {
      panel.classList.remove('hidden');
      icon.style.transform = 'rotate(180deg)';
      text.textContent = '收起配置';
    } else {
      panel.classList.add('hidden');
      icon.style.transform = 'rotate(0deg)';
      text.textContent = '展开配置';
    }
  });

  // 预设选择
  document.querySelectorAll('[data-preset]').forEach(elem => {
    elem.addEventListener('click', function () {
      const preset = this.dataset.preset;

      // 更新选择状态
      document.querySelectorAll('[data-preset]').forEach(el => {
        el.classList.remove('selected', 'bg-blue-600', 'border-blue-600', 'text-white');
        el.classList.add('bg-blue-50', 'border-slate-300');
      });

      this.classList.add('selected', 'bg-blue-600', 'border-blue-600', 'text-white');
      this.classList.remove('bg-blue-50', 'border-slate-300');

      // 保存预设选择
      saveCurrentPreset(preset);

      // 显示/隐藏自定义配置区域
      const customSection = document.getElementById('custom-config-section');
      if (preset === 'custom') {
        customSection.classList.remove('hidden');
        const config = getApiConfig();
        document.getElementById('api-base-url').value = config.baseUrl || '';
        document.getElementById('api-key').value = config.apiKey || '';
        document.getElementById('api-model').value = config.model || '';
      } else {
        customSection.classList.add('hidden');
      }
    });
  });

  // 保存配置按钮
  document.getElementById('save-config').addEventListener('click', function () {
    const currentPreset = getCurrentPreset();

    if (currentPreset === 'custom') {
      const config = {
        baseUrl: document.getElementById('api-base-url').value.trim(),
        apiKey: document.getElementById('api-key').value.trim(),
        model: document.getElementById('api-model').value.trim()
      };

      if (!config.baseUrl || !config.apiKey || !config.model) {
        showError('请填写完整的API配置信息');
        return;
      }

      saveApiConfig(config);
    }

    alert('配置已保存');
  });

  // 重置配置按钮
  document.getElementById('reset-config').addEventListener('click', function () {
    // 重置为默认预设
    saveCurrentPreset('preset1');
    localStorage.removeItem(API_CONFIG_STORAGE_KEY);

    // 更新UI
    initializeApiConfig();

    alert('配置已重置为默认');
  });
}

// 设置评估选项相关事件（已由评估配置系统处理）
function setupEvaluationOptionEvents() {
  // 新的评估配置系统已经处理了选项选择
  // 这个函数保留以防有其他旧版按钮需要处理
  console.log('评估选项事件设置完成（由评估配置系统处理）')
}

// 设置图片上传相关事件
function setupImageUploadEvents() {
  const imageUpload = document.getElementById('image-upload');
  const uploadZone = document.getElementById('upload-zone');
  const uploadFileBtn = document.getElementById('upload-file-btn');
  const cameraBtn = document.getElementById('camera-btn');
  const addMoreImagesBtn = document.getElementById('add-more-images-btn');
  const clearAllImagesBtn = document.getElementById('clear-all-images-btn');

  // 文件选择事件
  imageUpload.addEventListener('change', handleImageUpload);

  // 选择文件按钮
  uploadFileBtn.addEventListener('click', function () {
    imageUpload.click();
  });

  // 拍照按钮
  cameraBtn.addEventListener('click', openCamera);

  // 添加更多图片按钮
  if (addMoreImagesBtn) {
    addMoreImagesBtn.addEventListener('click', function () {
      // 检查数量限制
      if (uploadedImages.length >= 5) {
        showError('最多只能上传5张图片');
        return;
      }
      imageUpload.click();
    });
  }

  // 清空所有图片按钮
  if (clearAllImagesBtn) {
    clearAllImagesBtn.addEventListener('click', clearAllImages);
  }

  // 拖拽上传（支持多文件）
  uploadZone.addEventListener('dragover', function (e) {
    e.preventDefault();
    this.classList.add('border-blue-600', 'bg-blue-50');
  });

  uploadZone.addEventListener('dragleave', function (e) {
    e.preventDefault();
    this.classList.remove('border-blue-600', 'bg-blue-50');
  });

  uploadZone.addEventListener('drop', function (e) {
    e.preventDefault();
    this.classList.remove('border-blue-600', 'bg-blue-50');

    const files = Array.from(e.dataTransfer.files).filter(f => f.type.startsWith('image/'));
    if (files.length > 0) {
      handleImageFiles(files);
    }
  });

  // 粘贴上传
  document.addEventListener('paste', function (e) {
    const items = e.clipboardData.items;
    for (let item of items) {
      if (item.type.startsWith('image/')) {
        const file = item.getAsFile();
        handleImageFiles([file]);
        break;
      }
    }
  });

  // 拍照相关事件
  setupCameraEvents();
}

// 处理图片上传
function handleImageUpload(event) {
  const files = Array.from(event.target.files);
  if (files.length > 0) {
    handleImageFiles(files);
  }
}

// 处理多个图片文件
function handleImageFiles(files) {
  // 检查数量限制
  const remainingSlots = 5 - uploadedImages.length;
  if (remainingSlots <= 0) {
    showError('最多只能上传5张图片');
    return;
  }

  if (files.length > remainingSlots) {
    showError(`只能再上传${remainingSlots}张图片，请重新选择`);
    return;
  }

  const validFiles = files.filter(file => {
    if (!file.type.startsWith('image/')) {
      showError(`文件 ${file.name} 不是图片格式`);
      return false;
    }
    if (file.size > 10 * 1024 * 1024) {
      showError(`文件 ${file.name} 大小超过10MB限制`);
      return false;
    }
    return true;
  });

  if (validFiles.length === 0) return;

  // 处理每个有效的图片文件
  Promise.all(validFiles.map(file => processImageFile(file)))
    .then(imageData => {
      // 检测重复图片
      const newImages = [];
      const duplicateNames = [];

      imageData.forEach(newImage => {
        const isDuplicate = uploadedImages.some(existingImage =>
          existingImage.hash === newImage.hash
        );

        if (isDuplicate) {
          duplicateNames.push(newImage.fileName);
        } else {
          newImages.push(newImage);
        }
      });

      // 显示重复提示
      if (duplicateNames.length > 0) {
        if (duplicateNames.length === 1) {
          showError(`图片 "${duplicateNames[0]}" 已存在，已跳过重复上传`);
        } else {
          showError(`${duplicateNames.length}张图片重复，已跳过: ${duplicateNames.join(', ')}`);
        }
      }

      // 添加非重复的图片
      if (newImages.length > 0) {
        uploadedImages.push(...newImages);
        updateImagePreview();
        hideError();
      }
    })
    .catch(error => {
      showError('图片处理失败: ' + error.message);
    });
}

// 处理单个图片文件为Promise
function processImageFile(file) {
  return new Promise(async (resolve, reject) => {
    try {
      const reader = new FileReader();
      reader.onload = async function (e) {
        try {
          const base64Data = e.target.result.split(',')[1];

          // 生成图片内容哈希作为唯一标识
          const hash = await generateImageHash(base64Data);

          resolve({
            id: Date.now() + Math.random(), // 简单的唯一ID
            hash: hash, // 图片内容哈希
            base64: base64Data, // 移除data:image/...;base64,前缀
            mimeType: file.type,
            fileName: file.name,
            fileSize: file.size,
            fullDataUrl: e.target.result
          });
        } catch (hashError) {
          console.error('生成图片哈希失败:', hashError);
          reject(hashError);
        }
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    } catch (error) {
      reject(error);
    }
  });
}

// 生成图片内容哈希
async function generateImageHash(base64Data) {
  const encoder = new TextEncoder();
  const data = encoder.encode(base64Data);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('').substring(0, 16); // 取前16位作为简短哈希
}

// 更新图片预览显示
function updateImagePreview() {
  const uploadZone = document.getElementById('upload-zone');
  const imagePreviewContainer = document.getElementById('image-preview-container');
  const imagesGrid = document.getElementById('images-grid');
  const imageCount = document.getElementById('image-count');

  if (uploadedImages.length > 0) {
    uploadZone.classList.add('hidden');
    imagePreviewContainer.classList.remove('hidden');

    // 更新图片数量
    const remaining = 5 - uploadedImages.length;
    imageCount.textContent = uploadedImages.length;

    // 更新数量提示
    const imageCountSpan = imageCount.parentElement.querySelector('span:last-child');
    if (imageCountSpan) {
      if (remaining === 0) {
        imageCountSpan.textContent = '/5张图片 (已达上限)';
        imageCountSpan.className = 'text-red-600 font-medium';
      } else {
        imageCountSpan.textContent = `/5张图片 (还可上传${remaining}张)`;
        imageCountSpan.className = 'text-gray-600';
      }
    }

    // 更新"添加更多"按钮状态
    const addMoreBtn = document.getElementById('add-more-images-btn');
    if (addMoreBtn) {
      if (remaining === 0) {
        addMoreBtn.disabled = true;
        addMoreBtn.className = 'text-gray-400 text-sm font-medium flex items-center space-x-1 cursor-not-allowed';
        addMoreBtn.title = '已达到最大上传数量限制';
      } else {
        addMoreBtn.disabled = false;
        addMoreBtn.className = 'text-green-600 hover:text-green-800 text-sm font-medium flex items-center space-x-1';
        addMoreBtn.title = `还可以上传${remaining}张图片`;
      }
    }

    // 更新拍照按钮状态
    const cameraBtn = document.getElementById('camera-upload-btn');
    if (cameraBtn) {
      if (remaining === 0) {
        cameraBtn.disabled = true;
        cameraBtn.className = 'bg-gray-300 text-gray-500 px-4 py-2 rounded-lg text-sm font-medium flex items-center space-x-1 cursor-not-allowed';
        cameraBtn.title = '已达到最大上传数量限制';
      } else {
        cameraBtn.disabled = false;
        cameraBtn.className = 'bg-green-100 text-green-700 hover:bg-green-200 px-4 py-2 rounded-lg text-sm font-medium flex items-center space-x-1';
        cameraBtn.title = `还可以拍照上传${remaining}张图片`;
      }
    }

    // 清空并重新生成图片网格
    imagesGrid.innerHTML = '';

    uploadedImages.forEach((image, index) => {
      const imageCard = createImagePreviewCard(image, index);
      imagesGrid.appendChild(imageCard);
    });
  } else {
    uploadZone.classList.remove('hidden');
    imagePreviewContainer.classList.add('hidden');
  }
}

// 创建图片预览卡片
function createImagePreviewCard(image, index) {
  const card = document.createElement('div');
  card.className = 'relative group';

  card.innerHTML = `
    <img src="${image.fullDataUrl}" alt="预览图片 ${index + 1}" 
         class="w-full h-32 object-cover rounded-lg shadow-sm">
    <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
      <button onclick="removeImage(${index})" class="bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-red-600 transition-colors">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>
    <div class="absolute bottom-1 left-1 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
      ${index + 1}
    </div>
  `;

  return card;
}

// 移除单张图片（全局函数，供HTML onclick调用）
window.removeImage = function (index) {
  uploadedImages.splice(index, 1);
  updateImagePreview();
}

// 清空所有图片
function clearAllImages() {
  uploadedImages = [];
  updateImagePreview();

  // 重置文件输入
  const imageUpload = document.getElementById('image-upload');
  imageUpload.value = '';
}

// 设置拍照相关事件
function setupCameraEvents() {
  const takePhotoBtn = document.getElementById('take-photo-btn');
  const closeCameraBtn = document.getElementById('close-camera-btn');

  if (takePhotoBtn) {
    takePhotoBtn.addEventListener('click', takePhoto);
  }

  if (closeCameraBtn) {
    closeCameraBtn.addEventListener('click', closeCamera);
  }
}

// 打开摄像头
async function openCamera() {
  // 检查数量限制
  if (uploadedImages.length >= 5) {
    showError('最多只能上传5张图片');
    return;
  }

  try {
    const cameraArea = document.getElementById('camera-area');
    const video = document.getElementById('camera-video');

    // 请求摄像头权限
    cameraStream = await navigator.mediaDevices.getUserMedia({
      video: {
        width: { ideal: 1280 },
        height: { ideal: 720 },
        facingMode: 'environment' // 优先使用后置摄像头
      }
    });

    video.srcObject = cameraStream;
    cameraArea.classList.remove('hidden');

  } catch (error) {
    console.error('无法访问摄像头:', error);
    showError('无法访问摄像头，请检查权限设置');
  }
}

// 拍照
function takePhoto() {
  const video = document.getElementById('camera-video');
  const canvas = document.getElementById('camera-canvas');
  const ctx = canvas.getContext('2d');

  // 设置canvas尺寸与video相同
  canvas.width = video.videoWidth;
  canvas.height = video.videoHeight;

  // 将视频帧绘制到canvas
  ctx.drawImage(video, 0, 0);

  // 转换为blob然后处理
  canvas.toBlob(blob => {
    if (blob) {
      const file = new File([blob], `拍照_${Date.now()}.jpg`, { type: 'image/jpeg' });
      handleImageFiles([file]);
      closeCamera(); // 拍照后关闭摄像头
    }
  }, 'image/jpeg', 0.8);
}

// 关闭摄像头
function closeCamera() {
  if (cameraStream) {
    cameraStream.getTracks().forEach(track => track.stop());
    cameraStream = null;
  }

  const cameraArea = document.getElementById('camera-area');
  cameraArea.classList.add('hidden');
}

// 设置评估按钮事件
function setupEvaluationButtonEvents() {
  document.getElementById('evaluate-button').addEventListener('click', performEvaluation);

  // 结果按钮事件
  document.getElementById('try-again-button').addEventListener('click', function () {
    // 隐藏结果容器，允许重新评估
    document.getElementById('results-container').classList.add('hidden');

    // 重置评估结果显示
    const resultsContainer = document.getElementById('results-container');
    const h2 = resultsContainer.querySelector('h2');
    h2.textContent = '评估结果';

    // 显示单图结果显示区域
    document.querySelector('.flex.items-center.space-x-3').style.display = 'flex';
    document.querySelector('.bg-slate-50.border.border-gray-200.rounded-lg').style.display = 'block';

    // 隐藏多图结果显示区域
    const multiResults = document.getElementById('multi-results');
    if (multiResults) {
      multiResults.style.display = 'none';
    }

    // 重置按钮文本
    const buttonText = document.getElementById('button-text');
    buttonText.textContent = '查看评估的结果';
  });

  document.getElementById('save-button').addEventListener('click', saveEvaluation);
  document.getElementById('share-button').addEventListener('click', openShareModal);

  // 批量分享按钮事件
  const batchShareButton = document.getElementById('batch-share-button');
  if (batchShareButton) {
    batchShareButton.addEventListener('click', batchShareEvaluations);
  }
}

// 执行评估
async function performEvaluation() {
  if (uploadedImages.length === 0) {
    showError('请先上传图片');
    return;
  }

  showLoading();
  hideError();

  try {
    const config = getApiConfig();

    if (uploadedImages.length === 1) {
      // 单图评估
      const image = uploadedImages[0];
      const result = await apiClient.createEvaluation({
        image: image.base64,
        mimeType: image.mimeType,
        style: currentSelectedStyle,
        perspective: currentSelectedPerspective,
        detail: currentSelectedDetail,
        apiConfig: config,
        title: `图片评估 - ${image.fileName}`,
        description: `${currentSelectedStyle}风格 - ${currentSelectedPerspective}视角`
      });

      displayEvaluationResult(result, false);
    } else {
      // 多图评估
      const results = [];

      for (let i = 0; i < uploadedImages.length; i++) {
        const image = uploadedImages[i];

        // 更新加载状态显示当前处理的图片
        const button = document.getElementById('evaluate-button');
        const buttonText = document.getElementById('button-text');
        buttonText.textContent = `评估中... (${i + 1}/${uploadedImages.length})`;

        try {
          const result = await apiClient.createEvaluation({
            image: image.base64,
            mimeType: image.mimeType,
            style: currentSelectedStyle,
            perspective: currentSelectedPerspective,
            detail: currentSelectedDetail,
            apiConfig: config,
            title: `图片评估 - ${image.fileName}`,
            description: `${currentSelectedStyle}风格 - ${currentSelectedPerspective}视角`
          });

          results.push({
            ...result,
            imageName: image.fileName,
            imageData: image.fullDataUrl
          });
        } catch (error) {
          results.push({
            imageName: image.fileName,
            imageData: image.fullDataUrl,
            error: error.message || '评估失败'
          });
        }
      }

      displayMultipleEvaluationResults(results);
    }

  } catch (error) {
    console.error('评估错误:', error);

    // 如果是API限流错误且当前使用preset1，提示用户切换到preset2
    if (error.message && error.message.includes('rate_limit_exceeded') && getCurrentPreset() === 'preset1') {
      showError('主API繁忙，请尝试切换到预设2或自定义API配置');
    }

    showError(error.message || '评估过程中发生错误，请重试');
  } finally {
    hideLoading();
  }
}

// 显示评估结果
function displayEvaluationResult(result, isMultiple = false) {
  currentEvaluationResult = result;

  // 更新结果显示
  verdictElem.textContent = result.verdict || '未知';
  ratingElem.textContent = result.rating || '0';
  explanationElem.textContent = result.explanation || '无评估结果';

  // 设置评分表情
  const rating = parseInt(result.rating) || 0;
  ratingEmojiElem.textContent = getRatingEmoji(rating);

  // 显示结果容器
  document.getElementById('results-container').classList.remove('hidden');

  // 滚动到结果区域
  document.getElementById('results-container').scrollIntoView({
    behavior: 'smooth',
    block: 'start'
  });
}

// 显示多图评估结果
function displayMultipleEvaluationResults(results) {
  const resultsContainer = document.getElementById('results-container');
  const h2 = resultsContainer.querySelector('h2');

  // 修改标题
  h2.textContent = `多图评估结果 (${results.length}张图片)`;

  // 隐藏单图结果显示区域
  document.querySelector('.flex.items-center.space-x-3').style.display = 'none';
  document.querySelector('.bg-slate-50.border.border-gray-200.rounded-lg').style.display = 'none';

  // 创建多图结果显示区域
  let multiResultsContainer = document.getElementById('multi-results');
  if (!multiResultsContainer) {
    multiResultsContainer = document.createElement('div');
    multiResultsContainer.id = 'multi-results';
    multiResultsContainer.className = 'space-y-6 mb-6';
    resultsContainer.insertBefore(multiResultsContainer, resultsContainer.querySelector('.flex.flex-col.sm\\:flex-row'));
  }

  // 清空之前的结果
  multiResultsContainer.innerHTML = '';

  // 计算总体评分
  const validResults = results.filter(r => !r.error);
  const averageRating = validResults.length > 0 ?
    Math.round(validResults.reduce((sum, r) => sum + parseInt(r.rating || 0), 0) / validResults.length) : 0;

  // 添加总体评分显示
  const summaryCard = document.createElement('div');
  summaryCard.className = 'bg-blue-50 border border-blue-200 rounded-lg p-4';
  summaryCard.innerHTML = `
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold text-blue-900">总体评估</h3>
        <p class="text-blue-700">平均评分: <span class="text-2xl font-bold">${averageRating}/100</span> ${getRatingEmoji(averageRating)}</p>
        <p class="text-sm text-blue-600">成功评估: ${validResults.length}/${results.length} 张</p>
      </div>
    </div>
  `;
  multiResultsContainer.appendChild(summaryCard);

  // 为每张图片创建结果卡片
  results.forEach((result, index) => {
    const card = document.createElement('div');
    card.className = 'bg-white border border-gray-200 rounded-lg p-4 flex gap-4';

    if (result.error) {
      // 错误情况
      card.innerHTML = `
        <img src="${result.imageData}" alt="${result.imageName}" class="w-24 h-24 object-cover rounded-lg">
        <div class="flex-1">
          <h3 class="font-semibold text-gray-900 mb-2">📷 ${result.imageName}</h3>
          <div class="bg-red-50 border border-red-200 rounded-lg p-3">
            <p class="text-red-800 font-medium">评估失败</p>
            <p class="text-red-600 text-sm">${result.error}</p>
          </div>
        </div>
      `;
    } else {
      // 成功情况
      card.innerHTML = `
        <img src="${result.imageData}" alt="${result.imageName}" class="w-24 h-24 object-cover rounded-lg cursor-pointer" 
             onclick="openImageViewer('${result.imageData}')">
        <div class="flex-1">
          <div class="flex justify-between items-start mb-2">
            <h3 class="font-semibold text-gray-900">📷 ${result.imageName}</h3>
            <div class="text-right">
              <span class="text-2xl font-bold text-blue-600">${result.rating}/100</span>
              <span class="text-xl ml-1">${getRatingEmoji(parseInt(result.rating || 0))}</span>
            </div>
          </div>
          <p class="font-medium text-gray-800 mb-2">${result.verdict}</p>
          <p class="text-sm text-gray-600 line-clamp-3">${result.explanation}</p>
        </div>
      `;
    }

    multiResultsContainer.appendChild(card);
  });

  // 保存多图结果用于后续操作
  currentEvaluationResult = {
    isMultiple: true,
    results: results,
    averageRating: averageRating
  };

  // 显示结果容器
  resultsContainer.classList.remove('hidden');

  // 滚动到结果区域
  resultsContainer.scrollIntoView({
    behavior: 'smooth',
    block: 'start'
  });
}

// 获取评分对应的表情
function getRatingEmoji(rating) {
  if (rating >= 90) return '🔥';
  else if (rating >= 80) return '😍';
  else if (rating >= 70) return '😊';
  else if (rating >= 60) return '🙂';
  else if (rating >= 50) return '😐';
  else if (rating >= 40) return '😕';
  else if (rating >= 30) return '😞';
  else return '💀';
}

// 上传图片到CDN（通过v1 API）
async function uploadImageToCDN(imageBase64, mimeType, fileName) {
  try {
    // 将base64转换为Blob
    const base64Data = imageBase64.replace(/^data:image\/[a-z]+;base64,/, '');
    const byteCharacters = atob(base64Data);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: mimeType });

    // 创建File对象
    const file = new File([blob], fileName || 'evaluation-image.jpg', { type: mimeType });

    // 通过v1 API上传到CDN
    console.log('通过v1 API上传图片到CDN:', fileName);
    const uploadResult = await apiClient.uploadFile(file);
    console.log('v1 API上传成功:', uploadResult);

    if (!uploadResult.url) {
      throw new Error('API响应中没有图片URL');
    }

    return uploadResult.url;
  } catch (error) {
    console.error('图片上传失败:', error);
    throw error;
  }
}

// 保存评估结果
async function saveEvaluation() {
  if (!currentEvaluationResult) {
    showError('没有可保存的评估结果');
    return;
  }

  const token = localStorage.getItem('authToken');
  if (!token) {
    showError('请先登录');
    return;
  }

  try {
    // 检查是否设为公开
    const isPublic = document.getElementById('public-checkbox').checked;
    console.log('保存设置 - 是否公开:', isPublic);

    // 显示保存进度
    const saveButton = document.getElementById('save-button');
    const originalText = saveButton.textContent;
    saveButton.textContent = '保存中...';
    saveButton.disabled = true;

    if (currentEvaluationResult.isMultiple) {
      // 多图保存 - 先上传图片到CDN，再保存评估结果
      const validResults = currentEvaluationResult.results.filter(r => !r.error);
      let completedCount = 0;

      const savePromises = validResults.map(async result => {
        const image = uploadedImages.find(img => img.fileName === result.imageName);

        try {
          // 更新进度显示
          saveButton.textContent = `上传图片中... (${completedCount + 1}/${validResults.length})`;

          // 上传图片到CDN
          console.log(`正在上传图片到CDN: ${result.imageName}`);
          const imageUrl = await uploadImageToCDN(image.base64, image.mimeType, result.imageName);
          console.log(`图片上传成功: ${imageUrl}`);

          // 更新进度显示
          saveButton.textContent = `保存评估中... (${completedCount + 1}/${validResults.length})`;

          // 保存评估结果，使用CDN URL
          const saveResult = await apiClient.saveEvaluation({
            image: imageUrl, // 使用CDN URL而不是base64
            mimeType: image.mimeType,
            style: currentSelectedStyle,
            perspective: currentSelectedPerspective,
            detail: currentSelectedDetail,
            title: `图片评估 - ${result.imageName}`,
            description: `${currentSelectedStyle}风格 - ${currentSelectedPerspective}视角`,
            verdict: result.verdict,
            rating: result.rating,
            explanation: result.explanation,
            isPublic: isPublic // 传递公开设置
          });

          completedCount++;
          return saveResult;
        } catch (error) {
          console.error(`保存图片 ${result.imageName} 失败:`, error);
          throw error;
        }
      });

      await Promise.all(savePromises);

      // 如果设为公开，为每个结果创建公开分享
      if (isPublic) {
        saveButton.textContent = '创建公开分享中...';
        const sharePromises = validResults.map(async (result, index) => {
          const image = uploadedImages.find(img => img.fileName === result.imageName);
          const imageUrl = await uploadImageToCDN(image.base64, image.mimeType, image.fileName);
          return createPublicShare(result, imageUrl);
        });

        const shareResults = await Promise.all(sharePromises);
        console.log(`创建了 ${shareResults.length} 个公开分享`);
      }

      alert(`成功保存了 ${validResults.length} 张图片的评估结果${isPublic ? '并设为公开' : ''}`);
    } else {
      // 单图保存 - 先上传图片到CDN，再保存评估结果
      const image = uploadedImages[0];

      // 上传图片到CDN
      saveButton.textContent = '上传图片中...';
      console.log('正在上传图片到CDN...');
      const imageUrl = await uploadImageToCDN(image.base64, image.mimeType, image.fileName);
      console.log(`图片上传成功: ${imageUrl}`);

      // 保存评估结果，使用CDN URL
      saveButton.textContent = '保存评估中...';
      const result = await apiClient.saveEvaluation({
        image: imageUrl, // 使用CDN URL而不是base64
        mimeType: image.mimeType,
        style: currentSelectedStyle,
        perspective: currentSelectedPerspective,
        detail: currentSelectedDetail,
        title: `图片评估 - ${image.fileName}`,
        description: `${currentSelectedStyle}风格 - ${currentSelectedPerspective}视角`,
        verdict: currentEvaluationResult.verdict,
        rating: currentEvaluationResult.rating,
        explanation: currentEvaluationResult.explanation,
        isPublic: isPublic // 传递公开设置
      });

      // 如果设为公开，创建公开分享
      if (isPublic) {
        saveButton.textContent = '创建公开分享中...';
        await createPublicShare(currentEvaluationResult, imageUrl);
      }

      alert(`评估结果已保存到历史记录${isPublic ? '并设为公开' : ''}`);
      console.log('评估已保存:', result);
    }

    // 恢复按钮状态
    saveButton.textContent = originalText;
    saveButton.disabled = false;

    // 刷新历史评估列表（如果当前在个人评估标签页）
    if (document.getElementById('personal-tab').classList.contains('bg-blue-600')) {
      loadPersonalEvaluations();
    }
  } catch (error) {
    console.error('保存错误:', error);

    // 恢复按钮状态
    const saveButton = document.getElementById('save-button');
    if (saveButton) {
      saveButton.textContent = '保存结果';
      saveButton.disabled = false;
    }

    // 显示具体错误信息
    if (error.message && error.message.includes('CDN')) {
      showError('图片上传失败，请检查网络连接后重试');
    } else {
      showError(error.message || '保存过程中发生错误');
    }
  }
}

// 创建公开分享
async function createPublicShare(evaluationResult, imageUrl = null) {
  try {
    // 准备分享数据
    let shareImage = imageUrl;
    let shareMimeType = 'image/jpeg';

    // 如果没有提供imageUrl，从uploadedImages获取
    if (!shareImage && uploadedImages.length > 0) {
      const image = uploadedImages[0];
      shareImage = image.base64;
      shareMimeType = image.mimeType;
    }

    const shareData = {
      verdict: evaluationResult.verdict,
      rating: evaluationResult.rating,
      explanation: evaluationResult.explanation,
      image: shareImage,
      mimeType: shareMimeType,
      style: currentSelectedStyle,
      perspective: currentSelectedPerspective,
      detail: currentSelectedDetail,
      timestamp: new Date().toLocaleString(),
      isPublic: true // 设为公开
    };

    console.log('创建公开分享:', shareData);

    // 创建分享
    const result = await apiClient.createShare(shareData);
    console.log('公开分享创建成功:', result);

    return result;
  } catch (error) {
    console.error('创建公开分享失败:', error);
    throw error;
  }
}

// 打开分享模态框
function openShareModal() {
  if (!currentEvaluationResult) {
    showError('没有可分享的评估结果');
    return;
  }

  const modal = document.getElementById('share-modal');
  modal.classList.add('show');
  modal.style.display = 'flex';
}

// 关闭分享模态框
function closeShareModal() {
  const modal = document.getElementById('share-modal');
  modal.classList.remove('show');
  setTimeout(() => {
    modal.style.display = 'none';
  }, 300);
}

// 设置模态框事件
function setupModalEvents() {
  // 分享模态框事件
  document.getElementById('close-share-modal').addEventListener('click', closeShareModal);

  // 点击模态框背景关闭
  document.getElementById('share-modal').addEventListener('click', function (e) {
    if (e.target === this) {
      closeShareModal();
    }
  });

  // 分享选项事件
  document.getElementById('share-link-option').addEventListener('click', generateShareLink);
  document.getElementById('share-image-option').addEventListener('click', downloadShareImage);
  document.getElementById('share-weibo-option').addEventListener('click', shareToWeibo);
  document.getElementById('share-twitter-option').addEventListener('click', shareToTwitter);
}

// 生成分享链接
async function generateShareLink() {
  if (!currentEvaluationResult) {
    showError('没有可分享的评估结果');
    return;
  }

  try {
    // 获取分享用的图片数据
    let shareImage = null;
    let shareMimeType = null;

    if (currentEvaluationResult.isMultiple) {
      // 多图分享：使用第一张成功的图片
      const firstValidResult = currentEvaluationResult.results.find(r => !r.error);
      if (firstValidResult) {
        const image = uploadedImages.find(img => img.fileName === firstValidResult.imageName);
        shareImage = image?.base64;
        shareMimeType = image?.mimeType;
      }
    } else {
      // 单图分享
      const image = uploadedImages[0];
      shareImage = image?.base64;
      shareMimeType = image?.mimeType;
    }

    const shareData = {
      ...currentEvaluationResult,
      image: shareImage,
      mimeType: shareMimeType,
      timestamp: new Date().toLocaleString(),
      isPublic: false // 默认私有分享
    };

    let result;

    // 优先使用新的RESTful API
    try {
      result = await apiClient.createShare(shareData);
    } catch (apiError) {
      console.log('新API分享失败，尝试旧版API:', apiError.message);

      // 回退到旧版API
      result = await apiClient.legacyCreateShare(shareData);
    }

    const shareUrl = result.fullUrl || `${window.location.origin}/s/${result.shareId}`;

    // 复制到剪贴板
    await navigator.clipboard.writeText(shareUrl);

    // 显示预览
    document.getElementById('share-url').textContent = shareUrl;
    document.getElementById('share-preview').classList.remove('hidden');

    alert('分享链接已复制到剪贴板');
  } catch (error) {
    console.error('生成分享链接错误:', error);
    showError(error.message || '生成分享链接失败');
  }
}

// 下载分享图片
function downloadShareImage() {
  alert('图片下载功能开发中...');
}

// 分享到微博
function shareToWeibo() {
  if (!currentEvaluationResult) {
    showError('没有可分享的评估结果');
    return;
  }

  const text = `我在AI图片评估助手得到了${currentEvaluationResult.verdict}(${currentEvaluationResult.rating}/100)的评分！`;
  const url = `https://service.weibo.com/share/share.php?url=${encodeURIComponent(window.location.href)}&title=${encodeURIComponent(text)}`;
  window.open(url, '_blank');
}

// 分享到Twitter
function shareToTwitter() {
  if (!currentEvaluationResult) {
    showError('没有可分享的评估结果');
    return;
  }

  const text = `I got ${currentEvaluationResult.verdict}(${currentEvaluationResult.rating}/100) on AI Image Evaluation Assistant!`;
  const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(window.location.href)}`;
  window.open(url, '_blank');
}

// 设置认证相关事件
function setupAuthEvents() {
  // 登录按钮
  document.getElementById('login-btn').addEventListener('click', openAuthModal);

  // 退出按钮
  document.getElementById('logout-btn').addEventListener('click', logout);

  // 认证模态框关闭
  document.getElementById('close-auth-modal').addEventListener('click', closeAuthModal);

  // 认证表单提交
  document.getElementById('auth-form').addEventListener('submit', handleAuthSubmit);

  // 切换登录/注册
  document.getElementById('auth-switch').addEventListener('click', toggleAuthMode);

  // 忘记密码
  document.getElementById('forgot-password').addEventListener('click', showForgotPasswordMode);

  // 点击模态框背景关闭
  document.getElementById('auth-modal').addEventListener('click', function (e) {
    if (e.target === this) {
      closeAuthModal();
    }
  });

  // 初始化用户状态
  checkUserStatus();
}

// 检查用户登录状态
function checkUserStatus() {
  const token = localStorage.getItem('authToken');
  if (token) {
    // 设置API客户端的token
    apiClient.setToken(token);
    // 验证token有效性
    fetch('/api/v1/auth/me', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
      .then(response => {
        if (response.ok) {
          return response.json();
        } else {
          throw new Error('Token invalid');
        }
      })
      .then(data => {
        updateUserUI(data.user);
      })
      .catch(() => {
        localStorage.removeItem('authToken');
        // 清除API客户端的token
        apiClient.setToken(null);
        updateUserUI(null);
      });
  } else {
    updateUserUI(null);
  }
}

// 更新用户界面
function updateUserUI(user) {
  const loginBtn = document.getElementById('login-btn');
  const userInfo = document.getElementById('user-info');
  const username = document.getElementById('username');
  const saveOptions = document.getElementById('save-options');
  const personalTab = document.getElementById('personal-tab');

  if (user) {
    loginBtn.classList.add('hidden');
    userInfo.classList.remove('hidden');
    username.textContent = user.username;
    if (saveOptions) saveOptions.classList.remove('hidden');
    if (personalTab) personalTab.classList.remove('hidden');
  } else {
    loginBtn.classList.remove('hidden');
    userInfo.classList.add('hidden');
    if (saveOptions) saveOptions.classList.add('hidden');
    if (personalTab) personalTab.classList.add('hidden');
  }
}

// 打开认证模态框
function openAuthModal() {
  const modal = document.getElementById('auth-modal');
  modal.classList.remove('hidden');
  modal.classList.add('show');
  modal.style.display = 'flex';
}

// 关闭认证模态框
function closeAuthModal() {
  const modal = document.getElementById('auth-modal');
  modal.classList.remove('show');
  setTimeout(() => {
    modal.style.display = 'none';
    modal.classList.add('hidden');
  }, 300);
}

// 切换登录/注册模式
function toggleAuthMode() {
  const title = document.getElementById('auth-modal-title');
  const submitBtn = document.getElementById('auth-submit');
  const switchText = document.getElementById('auth-switch-text');
  const switchBtn = document.getElementById('auth-switch');
  const emailField = document.getElementById('email-field');
  const passwordField = document.getElementById('password-field');
  const forgotPasswordLink = document.getElementById('forgot-password-link');

  if (title.textContent === '登录') {
    // 切换到注册模式
    title.textContent = '注册';
    submitBtn.textContent = '注册';
    switchText.textContent = '已有账户？';
    switchBtn.textContent = '登录';
    emailField.classList.remove('hidden');
    passwordField.classList.remove('hidden');
    forgotPasswordLink.classList.add('hidden');

    // 设置邮箱为必填
    document.getElementById('auth-email').required = true;
  } else {
    // 切换到登录模式（包括从重置密码模式返回）
    title.textContent = '登录';
    submitBtn.textContent = '登录';
    switchText.textContent = '还没有账户？';
    switchBtn.textContent = '注册';
    emailField.classList.add('hidden');
    passwordField.classList.remove('hidden');
    forgotPasswordLink.classList.remove('hidden');

    // 恢复字段的必填状态
    document.getElementById('auth-email').required = false;
    document.getElementById('auth-password').required = true;
  }

  // 清空错误和成功信息
  document.getElementById('auth-error').classList.add('hidden');
  document.getElementById('auth-success').classList.add('hidden');
}

// 显示忘记密码模式
function showForgotPasswordMode() {
  const title = document.getElementById('auth-modal-title');
  const submitBtn = document.getElementById('auth-submit');
  const switchText = document.getElementById('auth-switch-text');
  const switchBtn = document.getElementById('auth-switch');
  const emailField = document.getElementById('email-field');
  const passwordField = document.getElementById('password-field');
  const forgotPasswordLink = document.getElementById('forgot-password-link');

  // 切换到重置密码模式
  title.textContent = '重置密码';
  submitBtn.textContent = '发送重置邮件';
  switchText.textContent = '想起密码了？';
  switchBtn.textContent = '返回登录';
  emailField.classList.remove('hidden');
  passwordField.classList.add('hidden');
  forgotPasswordLink.classList.add('hidden');

  // 设置邮箱为必填，密码为非必填
  document.getElementById('auth-email').required = true;
  document.getElementById('auth-password').required = false;

  // 清空错误和成功信息
  document.getElementById('auth-error').classList.add('hidden');
  document.getElementById('auth-success').classList.add('hidden');
}

// 客户端密码哈希函数
async function hashPasswordClient(password) {
  const encoder = new TextEncoder();
  const data = encoder.encode(password + 'client-salt-2024');
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

// 处理认证表单提交
async function handleAuthSubmit(e) {
  e.preventDefault();

  const title = document.getElementById('auth-modal-title').textContent;
  const username = document.getElementById('auth-username').value;
  const email = document.getElementById('auth-email').value;
  const password = document.getElementById('auth-password').value;

  // 重置密码模式
  if (title === '重置密码') {
    if (!username || !email) {
      showAuthError('请输入用户名和邮箱');
      return;
    }

    try {
      const response = await fetch('/api/v1/auth/reset-password-request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username, email })
      });

      const data = await response.json();

      if (response.ok) {
        showAuthSuccess(data.message);
        // 如果有重置链接，可以在开发模式下显示
        if (data.resetUrl) {
          console.log('重置密码链接:', data.resetUrl);
        }
      } else {
        showAuthError(data.error || '重置密码请求失败');
      }
    } catch (error) {
      showAuthError('网络错误，请重试');
    }
    return;
  }

  // 登录/注册模式
  if (!username || !password) {
    showAuthError('请填写所有必填字段');
    return;
  }

  try {
    // 客户端哈希密码
    const hashedPassword = await hashPasswordClient(password);

    const endpoint = title === '登录' ? '/api/v1/auth/login' : '/api/v1/auth/register';
    const body = title === '登录'
      ? { username, password: hashedPassword }
      : { username, email, password: hashedPassword };

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body)
    });

    const data = await response.json();

    if (response.ok) {
      localStorage.setItem('authToken', data.token);
      // 更新API客户端的token
      apiClient.setToken(data.token);
      updateUserUI(data.user);
      closeAuthModal();

      // 清空表单
      document.getElementById('auth-form').reset();
    } else {
      showAuthError(data.error || '操作失败');
    }
  } catch (error) {
    showAuthError('网络错误，请重试');
  }
}

// 显示认证错误
function showAuthError(message) {
  const errorDiv = document.getElementById('auth-error');
  errorDiv.textContent = message;
  errorDiv.classList.remove('hidden');
  // 隐藏成功信息
  document.getElementById('auth-success').classList.add('hidden');
}

// 显示认证成功信息
function showAuthSuccess(message) {
  const successDiv = document.getElementById('auth-success');
  successDiv.textContent = message;
  successDiv.classList.remove('hidden');
  // 隐藏错误信息
  document.getElementById('auth-error').classList.add('hidden');
}

// 退出登录
function logout() {
  localStorage.removeItem('authToken');
  // 清除API客户端的token
  apiClient.setToken(null);
  updateUserUI(null);
}

// 图片查看器函数
function closeImageViewer() {
  document.getElementById('image-viewer').style.display = 'none';
}

// 历史评估管理
let currentPersonalPage = 1;
let currentPublicPage = 1;
let hasMorePersonal = true;
let hasMorePublic = true;

// 加载个人评估列表
async function loadPersonalEvaluations(page = 1, append = false) {
  const token = localStorage.getItem('authToken');
  if (!token) {
    return;
  }

  try {
    const result = await apiClient.getEvaluations(page, 10);
    const container = document.getElementById('personal-evaluations-list');

    if (!append) {
      container.innerHTML = '';
    }

    if (result.evaluations && result.evaluations.length > 0) {
      result.evaluations.forEach(evaluation => {
        const evaluationCard = createEvaluationCard(evaluation, true);
        container.appendChild(evaluationCard);
      });

      hasMorePersonal = result.pagination.page < result.pagination.totalPages;
      currentPersonalPage = page;

      // 更新加载更多按钮
      const loadMoreBtn = document.getElementById('load-more-personal');
      if (hasMorePersonal) {
        loadMoreBtn.classList.remove('hidden');
      } else {
        loadMoreBtn.classList.add('hidden');
      }
    } else if (!append) {
      container.innerHTML = '<div class="text-center text-gray-500 py-8">暂无评估记录</div>';
    }
  } catch (error) {
    console.error('加载个人评估失败:', error);

    // 如果是权限错误，尝试刷新token
    if (error.message && (error.message.includes('权限') || error.message.includes('401') || error.message.includes('403'))) {
      console.log('检测到权限错误，尝试刷新token...');
      try {
        await apiClient.refreshToken();
        console.log('Token刷新成功，重新加载评估列表...');
        // 重新调用函数
        return loadPersonalEvaluations(page, append);
      } catch (refreshError) {
        console.error('Token刷新失败:', refreshError);
        alert('登录已过期，请重新登录');
        logout();
        return;
      }
    }

    if (!append) {
      document.getElementById('personal-evaluations-list').innerHTML =
        '<div class="text-center text-red-500 py-8">加载失败，请重试</div>';
    }
  }
}

// 创建评估卡片
function createEvaluationCard(evaluation, isPersonal = false) {
  const card = document.createElement('div');
  card.className = 'bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow';

  const imageHtml = evaluation.image_data ?
    `<img src="${evaluation.image_data}" alt="评估图片" class="w-16 h-16 object-cover rounded-lg cursor-pointer" onclick="openImageViewer('${evaluation.image_data}')">` :
    '<div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center text-gray-400">无图</div>';

  // 确保数据不为undefined
  const title = evaluation.title || '图片评估';
  const verdict = evaluation.verdict || '未知';
  const rating = evaluation.rating || 0;
  const explanation = evaluation.explanation || '无评估结果';
  const style = evaluation.style || '未知';
  const perspective = evaluation.perspective || '未知';
  const detail = evaluation.detail || '未知';
  const creatorUsername = evaluation.creator_username || '未知用户';
  const createdAt = evaluation.created_at ? new Date(evaluation.created_at).toLocaleDateString() : '未知时间';

  card.innerHTML = `
    <div class="flex gap-4">
      ${isPersonal ? `
        <div class="flex items-start pt-1">
          <input type="checkbox" class="evaluation-checkbox w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500" data-evaluation-id="${evaluation.id}">
        </div>
      ` : ''}
      ${imageHtml}
      <div class="flex-1 min-w-0">
        <div class="flex justify-between items-start mb-2">
          <h3 class="font-semibold text-gray-900 truncate">${title}</h3>
          <span class="text-2xl font-bold text-blue-600">${rating}/100</span>
        </div>
        <p class="text-sm text-gray-600 mb-2">${verdict}</p>
        <p class="text-xs text-gray-500 line-clamp-2">${explanation}</p>
        <div class="flex justify-between items-center mt-3">
          <div class="flex gap-2 text-xs">
            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded">${style}</span>
            <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded">${perspective}</span>
            <span class="bg-green-100 text-green-800 px-2 py-1 rounded">${detail}</span>
          </div>
          <span class="text-xs text-gray-400">${createdAt}</span>
        </div>
        ${isPersonal ? `
          <div class="flex gap-2 mt-3">
            <button onclick="viewEvaluationDetail('${evaluation.id}')" class="text-blue-600 hover:text-blue-800 text-xs">查看详情</button>
            <button onclick="shareEvaluation('${evaluation.id}')" class="text-green-600 hover:text-green-800 text-xs">分享</button>
            <button onclick="deleteEvaluation('${evaluation.id}')" class="text-red-600 hover:text-red-800 text-xs">删除</button>
          </div>
        ` : `
          <div class="flex gap-2 mt-3">
            <button onclick="viewEvaluationDetail('${evaluation.id}', true)" class="text-blue-600 hover:text-blue-800 text-xs">查看详情</button>
            <span class="text-xs text-gray-400">by ${creatorUsername}</span>
          </div>
        `}
      </div>
    </div>
  `;

  return card;
}

// 查看评估详情（全局函数，供HTML onclick调用）
window.viewEvaluationDetail = async function (evaluationId) {
  try {
    const result = await apiClient.getEvaluation(evaluationId);
    const evaluation = result.evaluation;

    // 显示详情模态框或跳转到详情页面
    alert(`评估详情：\n标题：${evaluation.title}\n评分：${evaluation.rating}/100\n结论：${evaluation.verdict}\n说明：${evaluation.explanation}`);
  } catch (error) {
    console.error('获取评估详情失败:', error);
    showError('获取评估详情失败');
  }
}

// 删除评估（全局函数，供HTML onclick调用）
window.deleteEvaluation = async function (evaluationId) {
  if (!confirm('确定要删除这个评估吗？')) {
    return;
  }

  try {
    console.log(`正在删除评估 ${evaluationId}`);
    const result = await apiClient.deleteEvaluation(evaluationId);
    console.log(`评估 ${evaluationId} 删除成功:`, result);
    alert('评估已删除');
    // 强制重新加载列表（清除缓存）
    currentPersonalPage = 1;
    await loadPersonalEvaluations(1, false);
  } catch (error) {
    console.error('删除评估失败:', error);
    showError(`删除评估失败: ${error.message}`);
  }
}

// 查看评估详情（全局函数，供HTML onclick调用）
window.viewEvaluationDetail = async function (evaluationId, isPublic = false) {
  try {
    console.log('查看评估详情:', evaluationId, '是否公开:', isPublic);

    // 根据是否公开选择不同的API
    let evaluation;
    if (isPublic) {
      evaluation = await apiClient.getPublicEvaluationDetail(evaluationId);
    } else {
      evaluation = await apiClient.getEvaluation(evaluationId);
    }
    console.log('评估详情:', evaluation);

    // 显示详情模态框
    showEvaluationDetailModal(evaluation.evaluation || evaluation);

  } catch (error) {
    console.error('获取评估详情失败:', error);
    showError(`获取评估详情失败: ${error.message}`);
  }
}

// 分享评估（全局函数，供HTML onclick调用）
window.shareEvaluation = async function (evaluationId) {
  try {
    console.log('分享评估:', evaluationId);

    // 获取评估详情
    const response = await apiClient.getEvaluation(evaluationId);
    const evaluation = response.evaluation || response;
    console.log('准备分享的评估:', evaluation);

    // 创建分享数据
    const shareData = {
      verdict: evaluation.verdict,
      rating: evaluation.rating,
      explanation: evaluation.explanation,
      image: evaluation.image_data,
      mimeType: evaluation.mime_type,
      style: evaluation.style,
      perspective: evaluation.perspective,
      detail: evaluation.detail,
      timestamp: new Date().toLocaleString(),
      isPublic: false // 默认私有分享
    };

    // 创建分享
    const result = await apiClient.createShare(shareData);
    const shareUrl = result.fullUrl || `${window.location.origin}/s/${result.id}`;

    // 复制到剪贴板
    await navigator.clipboard.writeText(shareUrl);
    alert('分享链接已复制到剪贴板！\n' + shareUrl);

  } catch (error) {
    console.error('分享评估失败:', error);
    showError(`分享评估失败: ${error.message}`);
  }
}

// 显示评估详情模态框
function showEvaluationDetailModal(evaluation) {
  // 确保数据不为undefined
  const title = evaluation.title || '无标题';
  const rating = evaluation.rating || 0;
  const verdict = evaluation.verdict || '未知';
  const explanation = evaluation.explanation || '无评估结果';
  const style = evaluation.style || '未知';
  const perspective = evaluation.perspective || '未知';
  const detail = evaluation.detail || '未知';
  const createdAt = evaluation.created_at ? new Date(evaluation.created_at).toLocaleString() : '未知时间';
  const imageData = evaluation.image_data || evaluation.image;
  const mimeType = evaluation.mime_type || 'image/jpeg';

  // 创建模态框HTML
  const modalHtml = `
    <div id="evaluation-detail-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div class="bg-white rounded-xiaomi-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6">
          <div class="flex justify-between items-start mb-4">
            <h3 class="text-xl font-bold text-xiaomi-text">评估详情</h3>
            <button onclick="closeEvaluationDetailModal()" class="text-gray-400 hover:text-gray-600 text-2xl">&times;</button>
          </div>

          <div class="space-y-6">
            <!-- 图片显示 -->
            ${imageData ? `
              <div class="text-center">
                <img src="${imageData.startsWith('http') ? imageData : `data:${mimeType};base64,${imageData}`}"
                     alt="评估图片" class="max-w-full max-h-64 mx-auto rounded-xiaomi shadow-lg">
              </div>
            ` : ''}

            <!-- 评分和评价 -->
            <div class="text-center">
              <div class="rating-badge inline-block text-2xl mb-2">${rating}/100</div>
              <div class="inline-block ml-4 px-4 py-2 rounded-full font-medium verdict-${verdict === '上' ? 'excellent' : verdict === '中' ? 'good' : 'poor'}">
                ${verdict}
              </div>
            </div>

            <!-- 评估说明 -->
            <div class="bg-gray-50 rounded-xiaomi p-4">
              <h4 class="font-semibold text-xiaomi-text mb-2">AI评估说明</h4>
              <p class="text-gray-700 leading-relaxed">${explanation}</p>
            </div>

            <!-- 评估参数 -->
            <div class="grid grid-cols-3 gap-4">
              <div class="text-center">
                <div class="bg-blue-100 text-blue-800 px-3 py-2 rounded-lg font-medium">
                  ${style}
                </div>
                <div class="text-xs text-gray-500 mt-1">评估风格</div>
              </div>
              <div class="text-center">
                <div class="bg-purple-100 text-purple-800 px-3 py-2 rounded-lg font-medium">
                  ${perspective}
                </div>
                <div class="text-xs text-gray-500 mt-1">评估视角</div>
              </div>
              <div class="text-center">
                <div class="bg-green-100 text-green-800 px-3 py-2 rounded-lg font-medium">
                  ${detail}
                </div>
                <div class="text-xs text-gray-500 mt-1">详细程度</div>
              </div>
            </div>

            <!-- 基本信息 -->
            <div class="border-t pt-4 text-sm text-gray-500">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <span class="font-medium">标题：</span>${title}
                </div>
                <div>
                  <span class="font-medium">创建时间：</span>${createdAt}
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex justify-center space-x-4 pt-4 border-t">
              <button onclick="shareEvaluation('${evaluation.id}')" class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-xiaomi transition-colors">
                分享评估
              </button>
              <button onclick="closeEvaluationDetailModal()" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-xiaomi transition-colors">
                关闭
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;

  // 添加到页面
  document.body.insertAdjacentHTML('beforeend', modalHtml);
}

// 关闭评估详情模态框
window.closeEvaluationDetailModal = function () {
  const modal = document.getElementById('evaluation-detail-modal');
  if (modal) {
    modal.remove();
  }
}

// 批量分享评估
async function batchShareEvaluations() {
  const selectedCheckboxes = document.querySelectorAll('.evaluation-checkbox:checked');
  const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.dataset.evaluationId);

  console.log('选中的评估ID:', selectedIds);

  if (selectedIds.length === 0) {
    alert('请先选择要分享的评估');
    return;
  }

  if (!confirm(`确定要分享选中的 ${selectedIds.length} 个评估吗？`)) {
    return;
  }

  try {
    const shareButton = document.getElementById('batch-share-btn');
    const originalText = shareButton.textContent;
    shareButton.textContent = '分享中...';
    shareButton.disabled = true;

    const sharePromises = selectedIds.map(async (evaluationId) => {
      try {
        // 获取评估详情
        const response = await apiClient.getEvaluation(evaluationId);
        const evaluation = response.evaluation || response;

        // 创建分享数据
        const shareData = {
          verdict: evaluation.verdict,
          rating: evaluation.rating,
          explanation: evaluation.explanation,
          image: evaluation.image_data,
          mimeType: evaluation.mime_type,
          style: evaluation.style,
          perspective: evaluation.perspective,
          detail: evaluation.detail,
          timestamp: new Date().toLocaleString(),
          isPublic: false
        };

        // 创建分享
        const result = await apiClient.createShare(shareData);
        return {
          evaluationId,
          shareUrl: result.fullUrl || `${window.location.origin}/s/${result.id}`,
          success: true
        };
      } catch (error) {
        console.error(`分享评估 ${evaluationId} 失败:`, error);
        return {
          evaluationId,
          error: error.message,
          success: false
        };
      }
    });

    const shareResults = await Promise.all(sharePromises);
    const successResults = shareResults.filter(r => r.success);
    const failedResults = shareResults.filter(r => !r.success);

    // 显示结果
    if (successResults.length > 0) {
      const shareUrls = successResults.map(r => r.shareUrl).join('\n');
      await navigator.clipboard.writeText(shareUrls);
      alert(`成功分享了 ${successResults.length} 个评估！\n分享链接已复制到剪贴板。\n\n${shareUrls}`);
    }

    if (failedResults.length > 0) {
      alert(`${failedResults.length} 个评估分享失败：\n${failedResults.map(r => `评估${r.evaluationId}: ${r.error}`).join('\n')}`);
    }

    // 恢复按钮状态
    shareButton.textContent = originalText;
    shareButton.disabled = false;

    // 隐藏批量操作控制栏
    hideBatchControls();

  } catch (error) {
    console.error('批量分享失败:', error);
    alert(`批量分享失败: ${error.message}`);

    // 恢复按钮状态
    const shareButton = document.getElementById('batch-share-btn');
    shareButton.textContent = '批量分享';
    shareButton.disabled = false;
  }
}

// 批量删除评估
async function batchDeleteEvaluations() {
  const selectedCheckboxes = document.querySelectorAll('.evaluation-checkbox:checked');
  const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.dataset.evaluationId);

  console.log('选中的评估ID:', selectedIds);

  if (selectedIds.length === 0) {
    alert('请先选择要删除的评估');
    return;
  }

  if (!confirm(`确定要删除选中的 ${selectedIds.length} 个评估吗？\n评估ID: ${selectedIds.join(', ')}\n此操作不可撤销。`)) {
    return;
  }

  try {
    const deleteButton = document.getElementById('batch-delete-btn');
    const originalText = deleteButton.textContent;
    deleteButton.textContent = '删除中...';
    deleteButton.disabled = true;

    // 调用批量删除API
    console.log('正在批量删除评估:', selectedIds);
    const result = await apiClient.batchDeleteEvaluations(selectedIds);
    console.log('批量删除结果:', result);

    // 显示删除结果
    if (result.notFoundIds && result.notFoundIds.length > 0) {
      alert(`删除完成：成功删除 ${result.deletedCount} 个评估\n${result.notFoundIds.length} 个评估不存在或无权限删除\n不存在的ID: ${result.notFoundIds.join(', ')}`);
    } else {
      alert(`成功删除了 ${result.deletedCount} 个评估`);
    }

    // 强制重新加载列表（清除缓存）
    currentPersonalPage = 1;
    await loadPersonalEvaluations(1, false);

    // 隐藏批量操作控制栏
    hideBatchControls();

  } catch (error) {
    console.error('批量删除失败:', error);
    showError(`批量删除失败: ${error.message}`);

    // 恢复按钮状态
    const deleteButton = document.getElementById('batch-delete-btn');
    deleteButton.textContent = '批量删除';
    deleteButton.disabled = false;
  }
}

// 显示批量操作控制栏
function showBatchControls() {
  document.getElementById('batch-controls').classList.remove('hidden');
  updateSelectedCount();
}

// 隐藏批量操作控制栏
function hideBatchControls() {
  document.getElementById('batch-controls').classList.add('hidden');
  // 清除所有选择
  document.querySelectorAll('.evaluation-checkbox').forEach(cb => cb.checked = false);
  document.getElementById('select-all-checkbox').checked = false;
  updateSelectedCount();
}

// 更新选中数量显示
function updateSelectedCount() {
  const selectedCheckboxes = document.querySelectorAll('.evaluation-checkbox:checked');
  const count = selectedCheckboxes.length;
  const totalCheckboxes = document.querySelectorAll('.evaluation-checkbox');

  document.getElementById('selected-count').textContent = `已选择 ${count} 项`;

  // 更新批量操作按钮状态
  const shareButton = document.getElementById('batch-share-btn');
  const deleteButton = document.getElementById('batch-delete-btn');
  if (shareButton) {
    shareButton.disabled = count === 0;
  }
  if (deleteButton) {
    deleteButton.disabled = count === 0;
  }

  // 更新全选复选框状态
  const selectAllCheckbox = document.getElementById('select-all-checkbox');
  if (count === 0) {
    selectAllCheckbox.indeterminate = false;
    selectAllCheckbox.checked = false;
  } else if (count === totalCheckboxes.length) {
    selectAllCheckbox.indeterminate = false;
    selectAllCheckbox.checked = true;
  } else {
    selectAllCheckbox.indeterminate = true;
    selectAllCheckbox.checked = false;
  }

  // 如果没有选中任何项，隐藏控制栏
  if (count === 0 && !document.getElementById('batch-controls').classList.contains('hidden')) {
    hideBatchControls();
  }
}

// 全选/取消全选
function toggleSelectAll() {
  const selectAllCheckbox = document.getElementById('select-all-checkbox');
  const evaluationCheckboxes = document.querySelectorAll('.evaluation-checkbox');

  evaluationCheckboxes.forEach(cb => {
    cb.checked = selectAllCheckbox.checked;
  });

  updateSelectedCount();
}

// 打开图片查看器（全局函数，供HTML onclick调用）
window.openImageViewer = function (imageSrc) {
  const viewer = document.getElementById('image-viewer');
  const viewerImage = document.getElementById('viewer-image');
  viewerImage.src = imageSrc;
  viewer.style.display = 'flex';
}

// 加载公开评估列表
async function loadPublicEvaluations(page = 1, append = false) {
  try {
    console.log(`加载公开评估列表 - 页码: ${page}, 追加: ${append}`);

    const response = await apiClient.getPublicEvaluations(page, 10);
    console.log('公开评估响应:', response);

    const publicList = document.getElementById('public-evaluations-list');
    if (!publicList) {
      console.error('找不到公开评估列表容器');
      return;
    }

    if (!append) {
      publicList.innerHTML = '';
    }

    if (!response.evaluations || response.evaluations.length === 0) {
      if (!append) {
        publicList.innerHTML = '<div class="text-center text-gray-500 py-8">暂无公开评估</div>';
      }
      return;
    }

    // 为每个评估创建卡片
    response.evaluations.forEach(evaluation => {
      const card = createEvaluationCard(evaluation, false); // false表示不是个人评估
      publicList.appendChild(card);
    });

    // 更新页码
    currentPublicPage = page;

    // 处理加载更多按钮
    const loadMoreButton = document.getElementById('load-more-public');
    if (loadMoreButton) {
      if (response.pagination.hasMore) {
        loadMoreButton.style.display = 'block';
      } else {
        loadMoreButton.style.display = 'none';
      }
    }

  } catch (error) {
    console.error('加载公开评估失败:', error);
    const publicList = document.getElementById('public-evaluations-list');
    if (publicList && !append) {
      publicList.innerHTML = '<div class="text-center text-red-500 py-8">加载失败，请重试</div>';
    }
  }
}

// 创建分享卡片
function createShareCard(share) {
  const card = document.createElement('div');
  card.className = 'bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow';

  // 处理图片显示
  let imageHtml = '';
  if (share.image) {
    const imageUrl = share.image.startsWith('http') ? share.image : `data:${share.mime_type || 'image/jpeg'};base64,${share.image}`;
    imageHtml = `
      <div class="w-20 h-20 flex-shrink-0">
        <img src="${imageUrl}" alt="分享图片" class="w-full h-full object-cover rounded-lg">
      </div>
    `;
  }

  card.innerHTML = `
    <div class="flex gap-4">
      ${imageHtml}
      <div class="flex-1 min-w-0">
        <div class="flex justify-between items-start mb-2">
          <h3 class="font-semibold text-gray-900 truncate">公开分享</h3>
          <span class="text-2xl font-bold text-blue-600">${share.rating}/100</span>
        </div>
        <p class="text-sm text-gray-600 mb-2">${share.verdict}</p>
        <p class="text-xs text-gray-500 line-clamp-2">${share.explanation}</p>
        <div class="flex justify-between items-center mt-3">
          <div class="flex gap-2 text-xs">
            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded">${share.style}</span>
            <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded">${share.perspective}</span>
            <span class="bg-green-100 text-green-800 px-2 py-1 rounded">${share.detail}</span>
          </div>
          <span class="text-xs text-gray-400">${new Date(share.created_at).toLocaleDateString()}</span>
        </div>
      </div>
    </div>
  `;

  return card;
}

// 设置批量操作事件监听
function setupBatchOperationEvents() {
  // 全选复选框事件
  const selectAllCheckbox = document.getElementById('select-all-checkbox');
  if (selectAllCheckbox) {
    selectAllCheckbox.addEventListener('change', toggleSelectAll);
  }

  // 批量分享按钮事件
  const batchShareBtn = document.getElementById('batch-share-btn');
  if (batchShareBtn) {
    batchShareBtn.addEventListener('click', batchShareEvaluations);
  }

  // 批量删除按钮事件
  const batchDeleteBtn = document.getElementById('batch-delete-btn');
  if (batchDeleteBtn) {
    batchDeleteBtn.addEventListener('click', batchDeleteEvaluations);
  }

  // 取消选择按钮事件
  const cancelBatchBtn = document.getElementById('cancel-batch-btn');
  if (cancelBatchBtn) {
    cancelBatchBtn.addEventListener('click', hideBatchControls);
  }

  // 监听个人评估列表的复选框变化
  document.addEventListener('change', function (e) {
    if (e.target.classList.contains('evaluation-checkbox')) {
      const checkedBoxes = document.querySelectorAll('.evaluation-checkbox:checked');
      if (checkedBoxes.length > 0) {
        showBatchControls();
      }
      updateSelectedCount();
    }
  });
}

// 初始化历史评估功能
function initializeEvaluationHistory() {
  // 批量操作事件监听
  setupBatchOperationEvents();

  // 标签页切换事件
  const publicTab = document.getElementById('public-tab');
  const personalTab = document.getElementById('personal-tab');
  const publicEvaluations = document.getElementById('public-evaluations');
  const personalEvaluations = document.getElementById('personal-evaluations');

  if (publicTab) {
    publicTab.addEventListener('click', () => {
      // 切换到公开评估标签
      publicTab.classList.add('bg-blue-600', 'text-white');
      publicTab.classList.remove('bg-gray-200', 'text-gray-700');
      personalTab.classList.remove('bg-blue-600', 'text-white');
      personalTab.classList.add('bg-gray-200', 'text-gray-700');

      publicEvaluations.classList.remove('hidden');
      personalEvaluations.classList.add('hidden');

      // 加载公开评估（如果还没有加载过）
      const publicList = document.getElementById('public-evaluations-list');
      if (publicList && publicList.children.length === 0) {
        loadPublicEvaluations();
      }
    });
  }

  if (personalTab) {
    personalTab.addEventListener('click', () => {
      // 切换到个人评估标签
      personalTab.classList.add('bg-blue-600', 'text-white');
      personalTab.classList.remove('bg-gray-200', 'text-gray-700');
      publicTab.classList.remove('bg-blue-600', 'text-white');
      publicTab.classList.add('bg-gray-200', 'text-gray-700');

      personalEvaluations.classList.remove('hidden');
      publicEvaluations.classList.add('hidden');

      // 加载个人评估（如果还没有加载过）
      const personalList = document.getElementById('personal-evaluations-list');
      if (personalList && personalList.children.length === 0) {
        loadPersonalEvaluations();
      }
    });
  }

  // 加载更多按钮事件
  const loadMorePersonal = document.getElementById('load-more-personal');
  if (loadMorePersonal) {
    loadMorePersonal.addEventListener('click', () => {
      loadPersonalEvaluations(currentPersonalPage + 1, true);
    });
  }

  const loadMorePublic = document.getElementById('load-more-public');
  if (loadMorePublic) {
    loadMorePublic.addEventListener('click', () => {
      loadPublicEvaluations(currentPublicPage + 1, true);
    });
  }
}

// 当页面加载完成时初始化应用
document.addEventListener('DOMContentLoaded', async () => {
  // 初始化评估配置
  if (window.evaluationConfig) {
    await window.evaluationConfig.initialize()

    // 监听评估配置变化
    document.addEventListener('evaluationConfigChanged', (e) => {
      const { type, value } = e.detail
      if (type === 'style') {
        currentSelectedStyle = value
      } else if (type === 'perspective') {
        currentSelectedPerspective = value
      } else if (type === 'detail') {
        currentSelectedDetail = value
      }
      console.log('评估配置已更新:', { type, value })
    })
  }

  // 初始化主应用
  initializeApp()
});
