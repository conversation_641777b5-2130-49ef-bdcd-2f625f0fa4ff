<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面未找到 - AI图片评估助手</title>
    
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- 小米风格字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- 小米风格配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'xiaomi-orange': '#FF6900',
                        'xiaomi-blue': '#0084FF',
                        'xiaomi-gray': '#F5F5F5',
                        'xiaomi-dark': '#1A1A1A',
                        'xiaomi-text': '#333333',
                        'xiaomi-light': '#FAFAFA'
                    },
                    fontFamily: {
                        'xiaomi': ['Inter', 'system-ui', '-apple-system', 'sans-serif']
                    },
                    borderRadius: {
                        'xiaomi': '12px',
                        'xiaomi-lg': '16px',
                        'xiaomi-xl': '20px'
                    },
                    boxShadow: {
                        'xiaomi': '0 2px 20px rgba(0, 0, 0, 0.08)',
                        'xiaomi-lg': '0 8px 40px rgba(0, 0, 0, 0.12)',
                        'xiaomi-hover': '0 4px 30px rgba(0, 0, 0, 0.15)'
                    }
                }
            }
        }
    </script>
    
    <style>
        /* 小米风格自定义样式 */
        .xiaomi-gradient {
            background: linear-gradient(135deg, #FF6900 0%, #FF8F00 100%);
        }
        
        .xiaomi-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .xiaomi-card:hover {
            box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }
        
        .xiaomi-button {
            background: linear-gradient(135deg, #FF6900 0%, #FF8F00 100%);
            border: none;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 20px rgba(255, 105, 0, 0.3);
        }
        
        .xiaomi-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 30px rgba(255, 105, 0, 0.4);
        }
        
        /* 动画效果 */
        @keyframes xiaomi-fade-in {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .xiaomi-fade-in {
            animation: xiaomi-fade-in 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        @keyframes xiaomi-float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }
        
        .xiaomi-float {
            animation: xiaomi-float 3s ease-in-out infinite;
        }
        
        /* 404数字样式 */
        .error-number {
            font-size: clamp(4rem, 15vw, 12rem);
            font-weight: 800;
            background: linear-gradient(135deg, #FF6900 0%, #FF8F00 50%, #FFB74D 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 4px 20px rgba(255, 105, 0, 0.3);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .xiaomi-card {
                border-radius: 12px;
                margin: 16px;
            }
            
            .xiaomi-button {
                border-radius: 10px;
            }
        }
    </style>
</head>
<body class="bg-xiaomi-light font-xiaomi min-h-screen flex items-center justify-center p-4">
    <div class="max-w-2xl w-full text-center">
        <!-- 404错误卡片 -->
        <div class="xiaomi-card p-8 md:p-12 xiaomi-fade-in">
            <!-- 404数字 -->
            <div class="xiaomi-float mb-8">
                <h1 class="error-number">404</h1>
            </div>
            
            <!-- 错误信息 -->
            <div class="mb-8">
                <h2 class="text-2xl md:text-3xl font-bold text-xiaomi-text mb-4">
                    页面走丢了 🤔
                </h2>
                <p class="text-lg text-gray-600 mb-6">
                    抱歉，您访问的页面不存在或已被移动
                </p>
                
                <!-- 可能的原因 -->
                <div class="bg-xiaomi-gray rounded-xiaomi-lg p-6 mb-8 text-left">
                    <h3 class="text-lg font-semibold text-xiaomi-text mb-4 flex items-center">
                        <div class="w-6 h-6 xiaomi-gradient rounded-lg mr-3 flex items-center justify-center">
                            <span class="text-white text-sm">💡</span>
                        </div>
                        可能的原因
                    </h3>
                    <ul class="space-y-2 text-gray-600">
                        <li class="flex items-center">
                            <span class="w-2 h-2 bg-xiaomi-orange rounded-full mr-3"></span>
                            网址输入错误
                        </li>
                        <li class="flex items-center">
                            <span class="w-2 h-2 bg-xiaomi-orange rounded-full mr-3"></span>
                            页面已被删除或移动
                        </li>
                        <li class="flex items-center">
                            <span class="w-2 h-2 bg-xiaomi-orange rounded-full mr-3"></span>
                            链接已过期
                        </li>
                        <li class="flex items-center">
                            <span class="w-2 h-2 bg-xiaomi-orange rounded-full mr-3"></span>
                            服务器临时故障
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button onclick="window.history.back()" class="bg-gray-100 hover:bg-gray-200 text-xiaomi-text px-8 py-3 rounded-xiaomi transition-colors flex items-center justify-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                    <span>返回上页</span>
                </button>
                <a href="/" class="xiaomi-button px-8 py-3 flex items-center justify-center space-x-2 text-decoration-none">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                    </svg>
                    <span>回到首页</span>
                </a>
            </div>
            
            <!-- 联系信息 -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <p class="text-sm text-gray-500">
                    如果问题持续存在，请联系我们的技术支持
                </p>
                <div class="flex justify-center items-center mt-4 space-x-6">
                    <a href="/" class="text-xiaomi-orange hover:text-orange-600 text-sm font-medium flex items-center space-x-1">
                        <span>🏠</span>
                        <span>首页</span>
                    </a>
                    <a href="/health" class="text-xiaomi-blue hover:text-blue-600 text-sm font-medium flex items-center space-x-1">
                        <span>💚</span>
                        <span>服务状态</span>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 底部信息 -->
        <div class="mt-8 text-center">
            <p class="text-sm text-gray-500">
                © 2025 AI图片评估助手 · 
                <span class="text-xiaomi-orange font-medium">Powered by Deno</span>
            </p>
        </div>
    </div>
</body>
</html>
