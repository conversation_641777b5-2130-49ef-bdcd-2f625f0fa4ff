/* 自定义样式 */
body {
  font-family: 'Inter', sans-serif;
  -webkit-overflow-scrolling: touch;
  -webkit-tap-highlight-color: transparent;
}

.input-file {
  display: none;
}

.loader {
  border: 4px solid #e2e8f0;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.checkmark-icon {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  color: #ffffff;
  font-size: 1.25rem;
  display: none;
}

@media (min-width: 768px) {
  .checkmark-icon {
    top: 0.75rem;
    right: 0.75rem;
    font-size: 1.5rem;
  }
}

/* 确保视角选择按钮在小屏幕下也能正常显示 */
@media (max-width: 767px) {
  .checkmark-icon {
    top: 0.25rem;
    right: 0.25rem;
    font-size: 1rem;
  }
}

.selected .checkmark-icon {
  display: block;
}

.saved-results-list::-webkit-scrollbar {
  width: 6px;
}

.saved-results-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.saved-results-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.saved-results-list::-webkit-scrollbar-thumb:hover {
  background: #3b82f6;
}

/* 简化的样式 */
.image-viewer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.image-viewer img {
  max-width: 90%;
  max-height: 90%;
  border-radius: 8px;
}

.image-viewer .close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 20px;
}

.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 50;
}

.modal.show {
  display: flex;
  justify-content: center;
  align-items: center;
}

.collapsed .text-content {
  max-height: 100px;
  overflow: hidden;
}

.expand-btn {
  color: #3b82f6;
  cursor: pointer;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.share-modal {
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.share-modal.show {
  opacity: 1;
  visibility: visible;
}

.share-modal-content {
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.share-modal.show .share-modal-content {
  transform: scale(1);
}

/* 认证模态框样式 */
.auth-modal {
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.auth-modal.show {
  opacity: 1;
  visibility: visible;
}

.auth-modal-content {
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.auth-modal.show .auth-modal-content {
  transform: scale(1);
}

/* 多图上传和拍照相关样式 */
.group:hover .opacity-0 {
  opacity: 1;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 摄像头视频流样式 */
#camera-video {
  transform: scaleX(-1);
  /* 镜像翻转，更符合用户习惯 */
}

/* 图片预览网格响应式布局 */
#images-grid {
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
}

@media (max-width: 640px) {
  #images-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  #images-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 移动端优化 */
@media (max-width: 640px) {

  /* 整体容器优化 */
  .w-full.mx-auto {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }

  /* 减少移动端的内边距 */
  .xiaomi-card {
    padding: 0.75rem !important;
    margin-bottom: 1rem !important;
  }

  /* 顶部导航栏优化 */
  nav {
    margin-bottom: 1rem !important;
  }

  nav .px-4 {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
    padding-top: 0.75rem !important;
    padding-bottom: 0.75rem !important;
  }

  /* 使用说明区域优化 */
  #disclaimer {
    padding: 0.75rem !important;
    margin-bottom: 1rem !important;
    font-size: 0.75rem !important;
  }

  /* API配置区域优化 */
  .bg-gray-50 {
    padding: 0.75rem !important;
    margin-bottom: 1rem !important;
  }

  /* 优化配置区域的间距 */
  .option-group {
    margin-bottom: 0.75rem !important;
  }

  /* 优化按钮文字大小 */
  .option-buttons button {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
  }

  /* 上传区域优化 */
  .flex.flex-col.items-center.space-y-4.mb-6 {
    margin-bottom: 1rem !important;
  }

  /* 评估按钮优化 */
  #evaluate-button {
    padding: 0.75rem 1.5rem !important;
    font-size: 1rem !important;
    margin-bottom: 1rem !important;
  }

  /* 优化标题文字大小 */
  h2 {
    font-size: 1.125rem !important;
  }

  h3 {
    font-size: 0.875rem !important;
  }

  /* 优化评估结果区域 */
  #results-container {
    padding: 0.75rem !important;
    margin-top: 1rem !important;
  }

  /* 优化评估结果标题 */
  #results-container h2 {
    font-size: 1.25rem !important;
    margin-bottom: 0.75rem !important;
  }

  /* 优化评分显示 */
  #verdict {
    font-size: 1.75rem !important;
  }

  #rating {
    font-size: 1.25rem !important;
  }

  /* 优化说明文字 */
  #explanation {
    font-size: 0.8rem;
    line-height: 1.4;
  }

  /* 评估结果列表区域优化 */
  #evaluations-section {
    margin-top: 1.5rem !important;
    padding-top: 1rem !important;
  }

  /* 评估结果标题优化 */
  #evaluations-section h2 {
    font-size: 1.125rem !important;
    margin-bottom: 1rem !important;
  }

  /* 标签按钮优化 */
  #public-tab,
  #personal-tab {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.75rem !important;
  }

  /* 评估卡片优化 */
  .evaluation-card {
    padding: 0.75rem !important;
    margin-bottom: 0.75rem !important;
  }

  /* 项目信息区域优化 */
  .bg-white.rounded-xl.shadow-lg {
    padding: 1rem !important;
    margin-top: 1.5rem !important;
  }

  /* 核心功能标签优化 */
  .grid.grid-cols-2 span {
    font-size: 0.7rem !important;
    padding: 0.25rem 0.5rem !important;
  }

  /* 拍照区域优化 */
  #camera-area {
    margin-top: 0.75rem !important;
  }

  #camera-area .bg-gray-100 {
    padding: 0.75rem !important;
  }

  #camera-video {
    max-width: 100% !important;
  }

  /* 拍照按钮优化 */
  #take-photo-btn,
  #close-camera-btn {
    padding: 0.5rem 1rem !important;
    font-size: 0.875rem !important;
  }

  /* 模态框优化 */
  .share-modal-content,
  .auth-modal-content {
    padding: 1rem !important;
    margin: 0.5rem !important;
  }

  /* 分享选项优化 */
  .share-modal-content .flex.items-center {
    padding: 0.75rem !important;
  }

  /* 表单输入框优化 */
  input[type="text"],
  input[type="password"],
  input[type="email"] {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
  }

  /* 加载更多按钮优化 */
  #load-more-public,
  #load-more-personal {
    padding: 0.5rem 1rem !important;
    font-size: 0.75rem !important;
  }

  /* 批量操作控制栏优化 */
  #batch-controls {
    padding: 0.75rem !important;
  }

  #batch-controls button {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.75rem !important;
  }
}

/* 图片预览卡片悬停效果 */
.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

/* 多图评估结果样式 */
#multi-results .bg-white:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.2s ease;
}

/* 摄像头按钮特殊样式 */
#camera-btn:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

/* 图片数量显示样式 */
#image-count {
  font-weight: 600;
  color: #3b82f6;
}

/* 总体评估卡片动画 */
.bg-blue-50 {
  position: relative;
  overflow: hidden;
}

.bg-blue-50::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.bg-blue-50:hover::before {
  left: 100%;
}
