/* 自定义样式 */
body {
  font-family: 'Inter', sans-serif;
  -webkit-overflow-scrolling: touch;
  -webkit-tap-highlight-color: transparent;
}

.input-file {
  display: none;
}

.loader {
  border: 4px solid #e2e8f0;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.checkmark-icon {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  color: #ffffff;
  font-size: 1.25rem;
  display: none;
}

@media (min-width: 768px) {
  .checkmark-icon {
    top: 0.75rem;
    right: 0.75rem;
    font-size: 1.5rem;
  }
}

/* 确保视角选择按钮在小屏幕下也能正常显示 */
@media (max-width: 767px) {
  .checkmark-icon {
    top: 0.25rem;
    right: 0.25rem;
    font-size: 1rem;
  }
}

.selected .checkmark-icon {
  display: block;
}

.saved-results-list::-webkit-scrollbar {
  width: 6px;
}

.saved-results-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.saved-results-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.saved-results-list::-webkit-scrollbar-thumb:hover {
  background: #3b82f6;
}

/* 简化的样式 */
.image-viewer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.image-viewer img {
  max-width: 90%;
  max-height: 90%;
  border-radius: 8px;
}

.image-viewer .close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 20px;
}

.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 50;
}

.modal.show {
  display: flex;
  justify-content: center;
  align-items: center;
}

.collapsed .text-content {
  max-height: 100px;
  overflow: hidden;
}

.expand-btn {
  color: #3b82f6;
  cursor: pointer;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.share-modal {
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.share-modal.show {
  opacity: 1;
  visibility: visible;
}

.share-modal-content {
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.share-modal.show .share-modal-content {
  transform: scale(1);
}

/* 认证模态框样式 */
.auth-modal {
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.auth-modal.show {
  opacity: 1;
  visibility: visible;
}

.auth-modal-content {
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.auth-modal.show .auth-modal-content {
  transform: scale(1);
}

/* 多图上传和拍照相关样式 */
.group:hover .opacity-0 {
  opacity: 1;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 摄像头视频流样式 */
#camera-video {
  transform: scaleX(-1);
  /* 镜像翻转，更符合用户习惯 */
}

/* 图片预览网格响应式布局 */
#images-grid {
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
}

@media (max-width: 640px) {
  #images-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  #images-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 图片预览卡片悬停效果 */
.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

/* 多图评估结果样式 */
#multi-results .bg-white:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.2s ease;
}

/* 摄像头按钮特殊样式 */
#camera-btn:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

/* 图片数量显示样式 */
#image-count {
  font-weight: 600;
  color: #3b82f6;
}

/* 总体评估卡片动画 */
.bg-blue-50 {
  position: relative;
  overflow: hidden;
}

.bg-blue-50::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.bg-blue-50:hover::before {
  left: 100%;
}
