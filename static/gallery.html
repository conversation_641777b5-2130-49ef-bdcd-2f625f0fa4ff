<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公开评估画廊 - SexUp AI评估系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- 小米风格字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- 小米风格配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'xiaomi-orange': '#FF6900',
                        'xiaomi-blue': '#0084FF',
                        'xiaomi-gray': '#F5F5F5',
                        'xiaomi-dark': '#1A1A1A',
                        'xiaomi-text': '#333333',
                        'xiaomi-light': '#FAFAFA'
                    },
                    fontFamily: {
                        'xiaomi': ['Inter', 'system-ui', '-apple-system', 'sans-serif']
                    },
                    borderRadius: {
                        'xiaomi': '12px',
                        'xiaomi-lg': '16px',
                        'xiaomi-xl': '20px'
                    },
                    boxShadow: {
                        'xiaomi': '0 2px 20px rgba(0, 0, 0, 0.08)',
                        'xiaomi-lg': '0 8px 40px rgba(0, 0, 0, 0.12)',
                        'xiaomi-hover': '0 4px 30px rgba(0, 0, 0, 0.15)'
                    }
                }
            }
        }
    </script>
    
    <style>
        /* 小米风格自定义样式 */
        .xiaomi-gradient {
            background: linear-gradient(135deg, #FF6900 0%, #FF8F00 100%);
        }
        
        .xiaomi-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .xiaomi-card:hover {
            box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }
        
        .gallery-card {
            break-inside: avoid;
            margin-bottom: 20px;
        }
        
        .masonry-grid {
            column-count: 1;
            column-gap: 20px;
        }
        
        @media (min-width: 640px) {
            .masonry-grid {
                column-count: 2;
            }
        }
        
        @media (min-width: 1024px) {
            .masonry-grid {
                column-count: 3;
            }
        }
        
        @media (min-width: 1280px) {
            .masonry-grid {
                column-count: 4;
            }
        }
        
        .rating-badge {
            background: linear-gradient(135deg, #FF6900 0%, #FF8F00 100%);
            color: white;
            font-weight: 700;
            font-size: 1.25rem;
            padding: 8px 16px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(255, 105, 0, 0.3);
        }
        
        .verdict-excellent {
            background: linear-gradient(135deg, #10B981 0%, #059669 100%);
            color: white;
        }
        
        .verdict-good {
            background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
            color: white;
        }
        
        .verdict-poor {
            background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
            color: white;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }
    </style>
</head>
<body class="bg-xiaomi-light font-xiaomi">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-xiaomi sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo和标题 -->
                <div class="flex items-center space-x-4">
                    <div class="w-8 h-8 xiaomi-gradient rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"/>
                        </svg>
                    </div>
                    <h1 class="text-xl font-bold text-xiaomi-text">公开评估画廊</h1>
                </div>
                
                <!-- 导航链接 -->
                <div class="flex items-center space-x-4">
                    <a href="/" class="bg-gray-100 hover:bg-gray-200 text-xiaomi-text px-4 py-2 rounded-xiaomi text-sm transition-colors">
                        返回首页
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 页面标题和描述 -->
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-xiaomi-text mb-4">AI评估画廊</h2>
            <p class="text-gray-600 text-lg max-w-2xl mx-auto">
                探索社区用户分享的精彩AI图片评估，发现不同视角下的美学观点
            </p>
        </div>

        <!-- 加载状态 -->
        <div id="loading" class="text-center py-12">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-xiaomi-orange"></div>
            <p class="mt-4 text-gray-600">正在加载精彩内容...</p>
        </div>

        <!-- 错误状态 -->
        <div id="error" class="text-center py-12 hidden">
            <div class="text-red-500 text-lg mb-4">加载失败</div>
            <button id="retry-btn" class="bg-xiaomi-orange hover:bg-orange-600 text-white px-6 py-2 rounded-xiaomi transition-colors">
                重试
            </button>
        </div>

        <!-- 空状态 -->
        <div id="empty" class="text-center py-12 hidden">
            <div class="text-gray-500 text-lg mb-4">暂无公开评估</div>
            <p class="text-gray-400">成为第一个分享评估的用户吧！</p>
            <a href="/" class="inline-block mt-4 bg-xiaomi-orange hover:bg-orange-600 text-white px-6 py-2 rounded-xiaomi transition-colors">
                开始评估
            </a>
        </div>

        <!-- 画廊网格 -->
        <div id="gallery" class="masonry-grid hidden">
            <!-- 动态生成的评估卡片 -->
        </div>

        <!-- 查看更多 -->
        <div id="load-more-container" class="text-center mt-12 hidden">
            <button id="load-more-btn" class="bg-white hover:bg-gray-50 text-xiaomi-text border border-gray-200 px-8 py-3 rounded-xiaomi transition-colors font-medium">
                查看更多精彩评估
            </button>
        </div>
    </main>

    <!-- 详情模态框 -->
    <div id="detail-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
        <div class="bg-white rounded-xiaomi-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-start mb-4">
                    <h3 class="text-xl font-bold text-xiaomi-text">评估详情</h3>
                    <button id="close-modal" class="text-gray-400 hover:text-gray-600 text-2xl">&times;</button>
                </div>
                <div id="modal-content">
                    <!-- 动态内容 -->
                </div>
            </div>
        </div>
    </div>

    <script src="/js/gallery.js"></script>
</body>
</html>
