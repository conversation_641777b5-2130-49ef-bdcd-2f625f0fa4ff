# 系统用户管理指南

## 概述

系统已自动为每个预定义角色创建对应的用户账户，方便测试和管理。

## 系统角色用户

| 角色 | 用户名 | 邮箱 | 密码 | 权限描述 |
|------|--------|------|------|----------|
| 超级管理员 | super_admin | <EMAIL> | super@123456 | 拥有所有权限 |
| 管理员 | admin | <EMAIL> | super@123456 | 大部分管理权限 |
| 版主 | moderator | <EMAIL> | super@123456 | 内容管理权限 |
| VIP用户 | vip_user | <EMAIL> | super@123456 | 高级功能权限 |
| 普通用户 | regular_user | <EMAIL> | super@123456 | 基础功能权限 |
| 访客 | guest | <EMAIL> | super@123456 | 只读权限 |

## 自动创建/更新机制

### 创建规则
- **用户名**：使用角色名作为用户名
- **邮箱**：格式为 `{角色名}@sexup.com`
- **密码**：统一为 `super@123456`
- **角色分配**：自动分配对应的系统角色

### 更新策略
- 如果用户已存在，**强制更新**邮箱和密码
- 确保角色分配正确
- 设置对应角色为默认角色

## 使用场景

### 1. 开发测试
```bash
# 登录不同角色进行功能测试
用户名: admin
密码: super@123456
```

### 2. 权限验证
```bash
# 测试不同权限级别
超级管理员: 所有功能
管理员: 用户管理、内容管理
版主: 内容审核
VIP用户: 高级评估功能
普通用户: 基础评估功能
访客: 只能查看公开内容
```

### 3. 演示环境
- 为客户演示时可以使用不同角色账户
- 展示不同权限级别的功能差异

## 管理脚本

### 测试系统用户创建
```bash
deno run --allow-net --allow-env scripts/test-system-users.ts
```

### 完整数据库重置（包含系统用户）
```bash
deno run --allow-net --allow-env scripts/reset-database-with-uuid.ts
```

## 安全注意事项

### 生产环境建议
1. **修改默认密码**：生产环境必须修改默认密码
2. **禁用不需要的账户**：删除或禁用不需要的系统用户
3. **定期审核**：定期检查系统用户的权限分配

### 密码安全
```bash
# 生产环境建议的密码策略
- 长度至少12位
- 包含大小写字母、数字、特殊字符
- 定期更换
- 不使用默认密码
```

## 权限矩阵

| 功能 | 超级管理员 | 管理员 | 版主 | VIP用户 | 普通用户 | 访客 |
|------|------------|--------|------|---------|----------|------|
| 用户管理 | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| 角色管理 | ✅ | 部分 | ❌ | ❌ | ❌ | ❌ |
| 内容审核 | ✅ | ✅ | ✅ | ❌ | ❌ | ❌ |
| 创建评估 | ✅ | ✅ | ✅ | ✅ | ✅ | ❌ |
| 公开分享 | ✅ | ✅ | ✅ | ✅ | ❌ | ❌ |
| 查看统计 | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| 系统配置 | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |

## 常见问题

### Q: 如何重置系统用户密码？
A: 运行系统用户创建脚本会自动重置所有系统用户的密码为默认值。

### Q: 可以删除系统用户吗？
A: 可以，但建议保留至少一个超级管理员账户。

### Q: 如何添加新的系统角色用户？
A: 在 `SYSTEM_ROLES` 中添加新角色，然后运行用户创建脚本。

### Q: 系统用户和普通用户有什么区别？
A: 系统用户是预定义的角色账户，普通用户是注册用户。功能上没有区别。

## 故障排除

### 用户创建失败
1. 检查数据库连接
2. 确认角色表已正确初始化
3. 查看错误日志

### 角色分配失败
1. 确认角色存在于 `sexup_roles` 表
2. 检查权限表是否正确初始化
3. 验证外键约束

### 登录失败
1. 确认用户名和密码正确
2. 检查用户是否被禁用
3. 验证密码哈希算法一致性
