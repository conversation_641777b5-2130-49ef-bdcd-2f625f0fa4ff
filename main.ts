import { Hono } from 'hono'
import { cors } from 'hono/cors'
import { serveStatic } from 'hono/deno'
import type { HonoContext } from './src/types/index.ts'

// 导入路由模块
import { setupApiRoutes } from './src/routes/api/index.ts'
import { setupShareRoutes } from './src/routes/share.ts'
import { initDatabase } from './src/utils/database.ts'

const app = new Hono()

// 启用 CORS
app.use('*', cors({
  origin: '*',
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
}))

// 静态文件服务
app.use('/css/*', serveStatic({ root: './static/', rewriteRequestPath: (path: string) => path.replace(/^\//, '') }))
app.use('/js/*', serveStatic({ root: './static/', rewriteRequestPath: (path: string) => path.replace(/^\//, '') }))
app.use('/favicon.ico', serveStatic({ path: './static/favicon.ico' }))

// 首页路由
app.get('/', serveStatic({ path: './static/index.html' }))

// 画廊页面路由
app.get('/gallery.html', serveStatic({ path: './static/gallery.html' }))

// API 路由
setupApiRoutes(app)

// 分享路由
setupShareRoutes(app)

// 健康检查
app.get('/health', (c: HonoContext) => {
  return c.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: '2.0.0'
  })
})

// SEO 路由
app.get('/sitemap.xml', (c: HonoContext) => {
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://sexup.deno.dev/</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>https://sexup.deno.dev/health</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.3</priority>
  </url>
</urlset>`
  
  c.header('Content-Type', 'application/xml')
  return c.text(sitemap)
})

app.get('/robots.txt', (c: HonoContext) => {
  const robots = `User-agent: *
Allow: /
Allow: /s/*
Disallow: /api/

Sitemap: https://sexup.deno.dev/sitemap.xml`
  
  c.header('Content-Type', 'text/plain')
  return c.text(robots)
})

// 管理页面
app.get('/admin', serveStatic({ path: './static/admin.html' }))

// 重置密码页面
app.get('/reset-password', (c) => {
  const token = c.req.query('token')
  return c.html(getResetPasswordPage(token))
})

// 重置密码页面HTML模板
function getResetPasswordPage(token?: string): string {
  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重置密码 - AI图片评估助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center p-4">
    <div class="bg-white rounded-xl shadow-lg p-6 md:p-8 max-w-md w-full">
        <div class="text-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900 mb-2">重置密码</h1>
            <p class="text-gray-600">请输入您的新密码</p>
        </div>

        <form id="reset-form" class="space-y-4">
            <input type="hidden" id="reset-token" value="${token || ''}">
            
            <div>
                <label for="new-password" class="block text-sm font-medium text-gray-700 mb-1">新密码</label>
                <input type="password" id="new-password" name="newPassword" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
                       placeholder="请输入新密码（至少6个字符）" required>
            </div>

            <div>
                <label for="confirm-password" class="block text-sm font-medium text-gray-700 mb-1">确认新密码</label>
                <input type="password" id="confirm-password" name="confirmPassword" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
                       placeholder="请再次输入新密码" required>
            </div>

            <button type="submit" id="reset-btn" 
                    class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 rounded-lg transition-colors">
                重置密码
            </button>
        </form>

        <div id="message" class="mt-4 text-center hidden"></div>

        <div class="text-center mt-6">
            <a href="/" class="text-blue-600 hover:text-blue-800 text-sm">返回首页</a>
        </div>
    </div>

    <script>
        // 客户端密码哈希函数
        async function hashPasswordClient(password) {
            const encoder = new TextEncoder();
            const data = encoder.encode(password + 'client-salt-2024');
            const hashBuffer = await crypto.subtle.digest('SHA-256', data);
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        }

        $(document).ready(function() {
            const token = $('#reset-token').val();
            
            if (!token) {
                showMessage('重置链接无效或已过期', 'error');
                $('#reset-form').hide();
                return;
            }

            $('#reset-form').on('submit', function(e) {
                e.preventDefault();
                
                const newPassword = $('#new-password').val();
                const confirmPassword = $('#confirm-password').val();
                
                if (newPassword.length < 6) {
                    showMessage('密码长度至少6个字符', 'error');
                    return;
                }
                
                if (newPassword !== confirmPassword) {
                    showMessage('两次输入的密码不一致', 'error');
                    return;
                }
                
                $('#reset-btn').prop('disabled', true).text('重置中...');
                
                // 对新密码进行客户端哈希
                hashPasswordClient(newPassword).then(hashedPassword => {
                    $.ajax({
                        url: '/api/v1/auth/reset-password',
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        data: JSON.stringify({
                            token: token,
                            newPassword: hashedPassword
                        }),
                    success: function(response) {
                        showMessage(response.message, 'success');
                        $('#reset-form').hide();
                        setTimeout(() => {
                            window.location.href = '/';
                        }, 3000);
                    },
                                            error: function(xhr) {
                            const response = xhr.responseJSON;
                            showMessage(response?.error || '重置密码失败', 'error');
                            $('#reset-btn').prop('disabled', false).text('重置密码');
                        }
                    });
                }).catch(error => {
                    console.error('密码哈希失败:', error);
                    showMessage('密码处理失败', 'error');
                    $('#reset-btn').prop('disabled', false).text('重置密码');
                });
            });
            
            function showMessage(message, type) {
                const messageEl = $('#message');
                messageEl.removeClass('hidden text-green-600 text-red-600');
                
                if (type === 'success') {
                    messageEl.addClass('text-green-600');
                } else {
                    messageEl.addClass('text-red-600');
                }
                
                messageEl.text(message).removeClass('hidden');
            }
        });
    </script>
</body>
</html>`;
}

// 404 页面处理 - 必须放在所有路由的最后
app.notFound((c) => {
  return c.html(Deno.readTextFileSync('./static/404.html'), 404)
})

// 启动时初始化数据库
initDatabase().catch(console.error)

// 启动服务器
const port = 8888
console.log(`🚀 服务器启动在 http://localhost:${port}`)

Deno.serve({ port }, app.fetch)
