# AI图片评估助手 - 重构版本

这是一个专业的多视角AI图片评估工具，支持夸夸机正能量模式、摄影师、美食家等8种专业视角的图片评估。

## 📁 项目结构

```
sexup/
├── deno.json                 # Deno配置和导入映射
├── main.ts                   # 应用入口点
├── default.ts               # 原始单文件版本（保留作参考）
├── src/
│   ├── routes/
│   │   ├── api/
│   │   │   ├── index.ts      # API路由汇总
│   │   │   ├── auth.ts       # 认证相关API
│   │   │   ├── evaluations.ts # 评估相关API  
│   │   │   └── share.ts      # 分享相关API
│   │   ├── share.ts          # 分享页面路由
│   │   └── static.ts         # 静态文件路由
│   ├── utils/
│   │   ├── database.ts       # 数据库操作
│   │   ├── auth.ts           # 认证工具函数
│   │   └── helpers.ts        # 其他工具函数
│   └── types/
│       └── index.ts          # 类型定义
├── static/
│   ├── index.html           # 主页面
│   ├── css/
│   │   └── styles.css       # 自定义样式
│   └── js/
│       └── app.js           # 前端逻辑
└── README.md
```

## 🚀 重构改进

### 1. 模块化结构
- **分离关注点**: 将认证、数据库、工具函数分离为独立模块
- **路由分组**: 按功能将API路由分组管理
- **静态文件分离**: HTML、CSS、JS分离，便于维护和缓存

### 2. 开发体验优化
- **热重载**: 使用 `deno task dev` 支持代码热重载
- **类型安全**: 完整的TypeScript类型定义
- **代码组织**: 清晰的目录结构和模块导入

### 3. 性能优化
- **静态文件服务**: 优化的静态文件缓存策略
- **模块懒加载**: 按需加载功能模块
- **CDN优化**: 外部资源使用CDN加速

## 📝 任务脚本

```bash
# 开发模式（热重载）
deno task dev

# 生产模式启动
deno task start

# 类型检查
deno task check
```

## 🔧 核心功能

### 1. 多视角评估
- **夸夸机模式**: 正能量满满的积极评价
- **专业视角**: 摄影师、美食家、艺术家等8种专业视角
- **评估详细度**: 简短、详细、深度三种分析模式

### 2. 用户系统
- **用户注册/登录**: 安全的密码哈希和JWT认证
- **个人评估**: 保存和管理个人评估历史
- **公开分享**: 设置评估结果的公开/私密状态

### 3. 分享功能
- **短链接**: 生成8位短链接便于分享
- **社交分享**: 支持微博、Twitter等社交平台
- **图片下载**: 生成包含评估结果的分享图片

## 🔧 API配置

支持多种AI服务配置：

1. **默认配置**: zone.veloera.org (gpt-4.1-mini)
2. **备用配置**: tbai.xin (gemini-1.5-flash-8b)  
3. **自定义配置**: 支持自定义API端点

## 📊 数据库

使用CockroachDB作为后端数据库：

- **用户表**: 存储用户认证信息
- **分享表**: 存储评估结果和分享数据
- **索引优化**: 针对查询性能的索引设计

## 🎨 前端技术

- **Tailwind CSS**: 现代化的UI设计
- **jQuery**: 简化的DOM操作
- **响应式设计**: 适配移动端和桌面端

## 🔒 安全特性

- **密码安全**: 客户端+服务端双重哈希
- **JWT认证**: 安全的用户会话管理
- **CORS配置**: 跨域请求安全控制
- **数据验证**: 严格的输入验证和过滤

## 🌐 部署信息

- **平台**: Deno Deploy
- **域名**: https://sexup.deno.dev/
- **状态监控**: /health 端点提供服务状态

## 📈 SEO优化

- **元标签**: 完整的SEO元数据
- **结构化数据**: Schema.org标记
- **Open Graph**: 社交媒体分享优化
- **Sitemap**: 自动生成网站地图
